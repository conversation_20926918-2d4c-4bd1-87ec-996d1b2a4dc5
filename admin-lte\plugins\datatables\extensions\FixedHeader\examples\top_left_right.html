<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">

	<title>FixedHeader example - Header, left and right all fixed</title>
	<link rel="stylesheet" type="text/css" href="../../../media/css/jquery.dataTables.css">
	<link rel="stylesheet" type="text/css" href="../css/dataTables.fixedHeader.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../examples/resources/demo.css">
	<style type="text/css" class="init">

	div.dataTables_wrapper {
		width: 150%;
	}

	div.FixedHeader_Cloned.fixedLeft tbody td {
		border-right: 1px solid black;
	}

	div.FixedHeader_Cloned.fixedRight tbody td {
		border-left: 1px solid black;
	}

	</style>
	<script type="text/javascript" language="javascript" src="../../../media/js/jquery.js"></script>
	<script type="text/javascript" language="javascript" src="../../../media/js/jquery.dataTables.js"></script>
	<script type="text/javascript" language="javascript" src="../js/dataTables.fixedHeader.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../examples/resources/demo.js"></script>
	<script type="text/javascript" language="javascript" class="init">


$(document).ready(function() {
	var table = $('#example').DataTable( {
		"order": [ 1, 'asc' ],
		"ajax": "../../../examples/ajax/data/objects.txt",
		"columns": [
			{ title: '',           data: null, defaultContent: "" },
			{ title: 'Name',       data: "name" },
			{ title: 'Position',   data: "position" },
			{ title: 'Office',     data: "office" },
			{ title: 'Extn.',      data: "extn" },
			{ title: 'Start date', data: "start_date" },
			{ title: 'Salary',     data: "salary" },
			{ title: '',           data: null, defaultContent: "" }
		],
		initComplete: function () {
			new $.fn.dataTable.FixedHeader( table, {
				left:   true,
				right:  true
			} );
		}
	} );

	table.on( 'order.dt search.dt', function () {
		table.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
			cell.innerHTML = i+1;
		} );

		table.column(-1, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
			cell.innerHTML = i+1;
		} );
	} ).draw();
} );


	</script>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>FixedHeader example <span>Header, left and right all fixed</span></h1>

			<div class="info">
				<p>FixedHeader provides the ability to fix in place the header, footer, left and right columns of the
				table. These are controlled by the options:</p>

				<ul class="markdown">
					<li><code>top</code> - default true</li>
					<li><code>bottom</code> - default false</li>
					<li><code>left</code> - default false</li>
					<li><code>right</code> - default false</li>
				</ul>

				<p>This example shows top, left and right enabled with index columns on the left and right.</p>

				<p>Note that in such a situation as this, the <a href=
				"//datatables.net/extensions/fixedcolumns">FixedColumns extension</a> might be more useful,
				particularly if you want to use the scrolling options built into DataTables.</p>
			</div>

			<table id="example" class="display" cellspacing="0" width="100%"></table>

			<ul class="tabs">
				<li class="active">Javascript</li>
				<li>HTML</li>
				<li>CSS</li>
				<li>Ajax</li>
				<li>Server-side script</li>
			</ul>

			<div class="tabs">
				<div class="js">
					<p>The Javascript shown below is used to initialise the table shown in this
					example:</p><code class="multiline brush: js;">$(document).ready(function() {
	var table = $('#example').DataTable( {
		&quot;order&quot;: [ 1, 'asc' ],
		&quot;ajax&quot;: &quot;../../../examples/ajax/data/objects.txt&quot;,
		&quot;columns&quot;: [
			{ title: '',           data: null, defaultContent: &quot;&quot; },
			{ title: 'Name',       data: &quot;name&quot; },
			{ title: 'Position',   data: &quot;position&quot; },
			{ title: 'Office',     data: &quot;office&quot; },
			{ title: 'Extn.',      data: &quot;extn&quot; },
			{ title: 'Start date', data: &quot;start_date&quot; },
			{ title: 'Salary',     data: &quot;salary&quot; },
			{ title: '',           data: null, defaultContent: &quot;&quot; }
		],
		initComplete: function () {
			new $.fn.dataTable.FixedHeader( table, {
				left:   true,
				right:  true
			} );
		}
	} );

	table.on( 'order.dt search.dt', function () {
		table.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
			cell.innerHTML = i+1;
		} );

		table.column(-1, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
			cell.innerHTML = i+1;
		} );
	} ).draw();
} );</code>

					<p>In addition to the above code, the following Javascript library files are loaded for use in this
					example:</p>

					<ul>
						<li><a href="../../../media/js/jquery.js">../../../media/js/jquery.js</a></li>
						<li><a href=
						"../../../media/js/jquery.dataTables.js">../../../media/js/jquery.dataTables.js</a></li>
						<li><a href="../js/dataTables.fixedHeader.js">../js/dataTables.fixedHeader.js</a></li>
					</ul>
				</div>

				<div class="table">
					<p>The HTML shown below is the raw HTML table element, before it has been enhanced by
					DataTables:</p>
				</div>

				<div class="css">
					<div>
						<p>This example uses a little bit of additional CSS beyond what is loaded from the library
						files (below), in order to correctly display the table. The additional CSS used is shown
						below:</p><code class="multiline brush: js;">div.dataTables_wrapper {
		width: 150%;
	}

	div.FixedHeader_Cloned.fixedLeft tbody td {
		border-right: 1px solid black;
	}

	div.FixedHeader_Cloned.fixedRight tbody td {
		border-left: 1px solid black;
	}</code>
					</div>

					<p>The following CSS library files are loaded for use in this example to provide the styling of the
					table:</p>

					<ul>
						<li><a href=
						"../../../media/css/jquery.dataTables.css">../../../media/css/jquery.dataTables.css</a></li>
						<li><a href="../css/dataTables.fixedHeader.css">../css/dataTables.fixedHeader.css</a></li>
					</ul>
				</div>

				<div class="ajax">
					<p>This table loads data by Ajax. The latest data that has been loaded is shown below. This data
					will update automatically as any additional data is loaded.</p>
				</div>

				<div class="php">
					<p>The script used to perform the server-side processing for this table is shown below. Please note
					that this is just an example script using PHP. Server-side processing scripts can be written in any
					language, using <a href="//datatables.net/manual/server-side">the protocol described in the
					DataTables documentation</a>.</p>
				</div>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<h2>Other examples</h2>

				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Examples</a></h3>
						<ul class="toc active">
							<li><a href="./simple.html">Basic initialisation</a></li>
							<li><a href="./header_footer.html">Header and footer fixed</a></li>
							<li class="active"><a href="./top_left_right.html">Header, left and right all
							fixed</a></li>
							<li><a href="./two_tables.html">Multiple tables</a></li>
							<li><a href="./zIndexes.html">z-index order control</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full
					information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and
					<a href="http://www.datatables.net/plug-ins">plug-ins</a> which extend the capabilities of
					DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href=
					"http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2014<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>