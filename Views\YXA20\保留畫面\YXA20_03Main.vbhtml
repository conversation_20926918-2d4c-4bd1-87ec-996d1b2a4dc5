﻿@Code
    If Request.IsAjaxRequest Then
        Layout = Nothing
    End If
    Dim ttSemiYear As String = ""
    Dim ttSemistry As String = "1"

    Dim id As String = "_" + Guid.NewGuid.ToString

    Dim ttSCL As List(Of TMyCode) = ViewData("SubClass")


End Code



<div id="@id" class="container-page100-white" style="align-items: normal;">
    <!--===============================================================================================-->
    <div class="View1 wrap-login100 p-b-30">
        <div id="divfixed1">
            @*功能名稱*@
            <span class="page100-form-label p-b-5 bo1-ColorM" style="color:black;">
                @*bo1-black*@
                @ViewData("FuncTitle")
            </span>

            @*注意事項*@
            @*<div class="row col-lg-12 col-md-12 col-sm-12 col-xs-12"></div>*@

            @*按鈕*@
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <input id="btGoHome" type="button" class="btn btn-default btn-Color3" value="返回首頁" />
                    <input id="btQryMain" type="button" class="btn btn-default btn-Color1" value="查詢" />
                </div>
            </div>

            @*查詢條件*@
            <div class="b2k-control">
                <div class="row">

                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="position:unset !important">
                        <label>權責單位：</label>
                        <select id="ddl_DutyOrg" class="ResQry">
                            <option value="">請選擇</option>
                            <option value="A506">教務處</option>
                            <option value="T999">體育室</option>
                            <option value="T101">醫學系</option>
                        </select>

                        <label>場地分類：</label>
                        <select id="ddl_SubClassEx" class="ResQry">
                            <option value="">請選擇</option>
                            @code
                                For Each ttobj In ttSCL
                                    @<option value="@ttobj.Code">@ttobj.Title</option>
                                Next
                            End Code
                        </select>

                        <label>場地：</label>
                        <select id="ddl_Res">
                            <option value="">請選擇</option>
                        </select>
                    </div>

                    

                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="position:unset !important">

                        <label>時間日期 ： </label>
                        <input type="text" id="tx_StartDate" class="txDatepicker" />
                        <input type="time" id="dt_StartTime" />
                        <label>～</label>
                        <input type="text" id="tx_StopDate" class="txDatepicker" />
                        <input type="time" id="dt_StopTime" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

                        <label>對象：</label>
                        <select id="ddl_Borrower">
                            <option value="0">請選擇</option>                                                 
                            <option value="1">學生</option>
                            <option value="2">教職員</option>                           
                            <option value="3">校外單位</option>                            
                        </select>

                    </div>

                </div>
            </div>
        </div>
        @*資料清單表*@
        <div class="row b2k-control">
            <div id="divNfixed1" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="overflow-y:auto;">
                <Table id="Tbl_main" class="table table-bordered table-hover dataTable no-footer rwd-table" style="width:100%;">
                    <thead>
                        <tr role="row">
                            <th style="padding:1em;">操作</th>
                            <th style="padding:1em;display:none">objid</th>
                            <th style="padding:1em;">權責單位</th>
                            <th style="padding:1em;">場地名稱</th>                            
                            <th style="padding:1em;">開放時間</th>
                            <th style="padding:1em;">開放對象</th>                            
                        </tr>
                    </thead>
                    <tbody></tbody>
                </Table>
            </div>
        </div>


    </div>
    <div class="View2 wrap-login100 p-b-30" style="display:none;">

    </div>
</div>
<script type="text/javascript">
    (function ($) {
        $(function () {
            var sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
            var ss_Header_height = $('.limiter').height(); //__Header.vbhtml的高度
            var ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
            var ssfixed1_height = $('#@id #divfixed1').height(); //除了資料清單表資料(功能名稱、注意事項、....)的高度
            var ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表資料的高度
            var ssbFilter_height = 31; //搜尋高度:固定高度無須調整
            var ssInfoEmpty_height = 20; //顯示筆數高度(顯示第 0 至 0 項結果，共 0 項):固定高度無須調整
            var ssTblheader_height = 90; //資料清單表頭高度:不固定請自行調整(當表頭越高，值越大，相反的，當表頭越低，值越小)
            var ssTbl; //資料清單表

            var ssSC1 = JSON.parse('@Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewData("SubClass_1")))'); //轉成JavaScript物件;
            var ssSC2 = JSON.parse('@Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(ViewData("SubClass_2")))'); //轉成JavaScript物件;

            //-----畫面執行-----
            CreateTbl($('#@id #Tbl_main'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
            //------------------

            //-----觸發事件-----
            //查詢場地
            $('#@id .ResQry').on('change', function () {
                QryRes();
            });

            //表單控制
            @*$('#@id #ddl_MainInfoNo').change(function () {
                $('#@id #ddl_SubClassEx').find('option').detach();
                var ttOption = '<option value="">請選擇</option>';
                var ttSC = $(this).val();
                if (ttSC = '1300100') {
                    $.map(ssSC1, function (item) {
                        ttOption += '<option value="' + item.Code + '">' + item.Title + '</option>';
                    });

                } else {
                    $.map(ssSC2, function (item) {
                        ttOption += '<option value="' + item.Code + '">' + item.Title + '</option>';
                    });
                }
                $('#@id #ddl_SubClassEx').append(ttOption);
            });*@

            //查詢場地
            $('#@id .ResQry').on('change', function () {
                QryRes();
            });

            
            //------------------

            //-----click事件-----
            //查詢
            $('#@id #btQryMain').click(function () {
                QryClick();
            });

            //返回首頁
            $('#@id #btGoHome').click(function () {
                $(this).trigger('YXA20_03Main_Close');
            })
            
            //詳細
            $('#@id #Tbl_main').on('click', '.clDetail', function () {
                var ttGetReqL = [];
                var pobj = $(this).closest('tr');
                var ttKeyVaule = $(pobj).find('.ColRes').text();                        
                     
                console.log(ttKeyVaule)
                ttGetReqL.push(ttKeyVaule);  

                    if (ttGetReqL.length === 0) {
                        alert("請先選取至少一筆資料！");
                        return;
                    }
                    
                    var url = '@Url.Action("YXA20_03Detail", "YXA20")';  
                    $.post(url, { ppGetReqL: ttGetReqL}, function (data) {
                        $('#@id>.View1').hide();
                        $('#@id>.View2').empty().show().html(data);
                    })
            });
            //-------------------

            //-----欄位格式設定-----
            $('#@id .txDatepicker').datepicker({
                format: "twy/mm/dd",
                weekStart: 1,
                maxViewMode: 1,
                language: "zh-TW"
            });

            
            //----------------------

            //------dialog------
            //------------------

            //-----function-----
            function QryClick() {
                var ttobj = {};
                ttobj.DutyOrgID = $('#@id #ddl_DutyOrg').val();
                //ttobj.MainInfoNo = $('#@id  #ddl_MainInfoNo').val();
                ttobj.SubClassEx = $('#@id  #ddl_SubClassEx').val();
                ttobj.ResID = $('#@id  #ddl_Res').val();
                //ttobj.Contains = $('#@id  #Contains').val();
                //ttobj.IsRent = ttobj.IsRent = $('#@id #state').is(':checked');
                
                ttobj.BorrowerID = $('#@id #ddl_Borrower').val();

                ttobj.StartDate = $('#@id  #tx_StartDate').val();
                ttobj.StartTime = $('#@id  #dt_StartTime').val();
                ttobj.StopDate = $('#@id  #tx_StopDate').val();
                ttobj.StopdTime = $('#@id  #dt_StopTime').val();

                var urlJSON = '@Url.Action("YXA20_03Main_Qry", "YXA20")';
                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppqmodel: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            $('#@id #Tbl_main').find('tbody').detach();
                            $('#Tbl_main').append('<tbody>');

                            $.map(data.obj, function (item) {
                                var tr = $('<tr class="ListRow">');
                                $('<td class="tools" >')
                                    .append('<button type="button" title="詳細" class="clDetail"><i class="glyphicon glyphicon-pencil">')                                    
                                    .appendTo(tr);
                                $('<td headers="objid" Class="Colobjid" style="display:none">' + item.objid + ' </td>').appendTo(tr);
                                $('<td headers="DutyOrg" Class="ColDutyOrg" value="' + item.DutyOrgID + '">' + item.DutyOrgName + ' </td>').appendTo(tr);
                                $('<td headers="Res" Class="ColRes" value="' + item.ResID + '">' + item.ResName + '</td>').appendTo(tr);                                
                                $('<td headers="OPDT" Class="ColOPDT">' + item.StartDate+" "+ item.StartTime + " ~ " +item.StopDate+" "+item.StopTime+'</td>').appendTo(tr);
                                $('<td headers="Borrower" Class="ColBorrower">' + item.Borrower + '</td>').appendTo(tr);
                                

                                $('#Tbl_main').append(tr)
                            })

                            CreateTbl($('#@id #Tbl_main'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
                        } else {
                            alert(data.MSG);
                        };
                    }
                })
            };

            //查詢物件(場地/設備)
            function QryRes() {
                var ttobj = {};
                ttobj.DutyOrgID = $('#@id #ddl_DutyOrg').val();
                ttobj.SubClassEx = $('#@id  #ddl_SubClassEx').val();

                var urlJSON = '@Url.Action("F_GetCode_Res", "YXA20")';
                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppqmodel: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            var ttOption = '<option value="">請選擇</option>';

                            $.map(data.obj, function (item) {
                                ttOption += '<option value="' + item.Code + '">' + item.Title + '</option>';
                            })

                            $('#@id #ddl_Res').find('option').detach();
                            $('#@id #ddl_Res').append(ttOption);

                        } else {
                            alert(data.MSG);
                        };
                    }
                })
            };

            //詳細
            function DetailClick(ttobj) {
                var url = '@Url.Action("YXA20_03Detail", "YXA20")';
                $.post(url, { pmodel: ttobj}, function (data) {
                    $('#@id>.View1').hide();
                    $('#@id>.View2').empty().show().html(data);
                })
            }


            function CreateTbl(ppTbl, ppbFilter, ppscrollY) {
                ssTbl = ppTbl.DataTable({
                    "bFilter": ppbFilter,  // 搜尋
                    "bLengthChange": true,
                    "paging": false,        //分頁
                    "bAutoWidth": false,
                    "fixedHeader": true, //表頭固定
                    "scrollY": ppscrollY, // 超過(設定值)時， 顯示卷軸
                    //"scrollX": auto, // auto
                    //固定首列，需要引入相應的dataTables.fixedColumns.min.js
                    "fixedColumns": {
                        "leftColumns": 1 //最左側1列固定
                    },

                    deferRender: true,
                    destroy: true,
                    scroller: true,
                    responsive: false
                });
            }

            $(window).resize(function () {
                sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
                ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
                ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表資料的高度
                $('#@id .View1').css('height', ssView1_height + 'px');
                $('#@id #divNfixed1').css('height', ssNfixed1_height + 'px');
                CreateTbl($('#@id #Tbl_main'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
                ssTbl.columns.adjust();
            });
            //------------------

        });
    })(jQuery);


</script>
