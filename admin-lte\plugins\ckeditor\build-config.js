﻿/**
 * @license Copyright (c) 2003-2016, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see LICENSE.md or http://ckeditor.com/license
 */

/**
 * This file was added automatically by CKEditor builder.
 * You may re-use it at any time to build CKEditor again.
 *
 * If you would like to build CKEditor online again
 * (for example to upgrade), visit one the following links:
 *
 * (1) http://ckeditor.com/builder
 *     Visit online builder to build CKEditor from scratch.
 *
 * (2) http://ckeditor.com/builder/ba0d86b4a03f476b450cdf7d3057be62
 *     Visit online builder to build CKEditor, starting with the same setup as before.
 *
 * (3) http://ckeditor.com/builder/download/ba0d86b4a03f476b450cdf7d3057be62
 *     Straight download link to the latest version of CKEditor (Optimized) with the same setup as before.
 *
 * NOTE:
 *    This file is not used by CKEditor, you may remove it.
 *    Changing this file will not change your CKEditor configuration.
 */

var CKBUILDER_CONFIG = {
	skin: 'moono',
	preset: 'standard',
	ignore: [
		'.bender',
		'bender.js',
		'bender-err.log',
		'bender-out.log',
		'dev',
		'.DS_Store',
		'.editorconfig',
		'.gitattributes',
		'.gitignore',
		'gruntfile.js',
		'.idea',
		'.jscsrc',
		'.jshintignore',
		'.jshintrc',
		'less',
		'.mailmap',
		'node_modules',
		'package.json',
		'README.md',
		'tests'
	],
	plugins : {
		'a11yhelp' : 1,
		'about' : 1,
		'basicstyles' : 1,
		'blockquote' : 1,
		'clipboard' : 1,
		'contextmenu' : 1,
		'elementspath' : 1,
		'enterkey' : 1,
		'entities' : 1,
		'filebrowser' : 1,
		'floatingspace' : 1,
		'format' : 1,
		'horizontalrule' : 1,
		'htmlwriter' : 1,
		'image' : 1,
		'indentlist' : 1,
		'link' : 1,
		'list' : 1,
		'magicline' : 1,
		'maximize' : 1,
		'pastefromword' : 1,
		'pastetext' : 1,
		'removeformat' : 1,
		'resize' : 1,
		'scayt' : 1,
		'showborders' : 1,
		'sourcearea' : 1,
		'specialchar' : 1,
		'stylescombo' : 1,
		'tab' : 1,
		'table' : 1,
		'tabletools' : 1,
		'toolbar' : 1,
		'undo' : 1,
		'wsc' : 1,
		'wysiwygarea' : 1
	},
	languages : {
		'af' : 1,
		'ar' : 1,
		'bg' : 1,
		'bn' : 1,
		'bs' : 1,
		'ca' : 1,
		'cs' : 1,
		'cy' : 1,
		'da' : 1,
		'de' : 1,
		'de-ch' : 1,
		'el' : 1,
		'en' : 1,
		'en-au' : 1,
		'en-ca' : 1,
		'en-gb' : 1,
		'eo' : 1,
		'es' : 1,
		'et' : 1,
		'eu' : 1,
		'fa' : 1,
		'fi' : 1,
		'fo' : 1,
		'fr' : 1,
		'fr-ca' : 1,
		'gl' : 1,
		'gu' : 1,
		'he' : 1,
		'hi' : 1,
		'hr' : 1,
		'hu' : 1,
		'id' : 1,
		'is' : 1,
		'it' : 1,
		'ja' : 1,
		'ka' : 1,
		'km' : 1,
		'ko' : 1,
		'ku' : 1,
		'lt' : 1,
		'lv' : 1,
		'mk' : 1,
		'mn' : 1,
		'ms' : 1,
		'nb' : 1,
		'nl' : 1,
		'no' : 1,
		'pl' : 1,
		'pt' : 1,
		'pt-br' : 1,
		'ro' : 1,
		'ru' : 1,
		'si' : 1,
		'sk' : 1,
		'sl' : 1,
		'sq' : 1,
		'sr' : 1,
		'sr-latn' : 1,
		'sv' : 1,
		'th' : 1,
		'tr' : 1,
		'tt' : 1,
		'ug' : 1,
		'uk' : 1,
		'vi' : 1,
		'zh' : 1,
		'zh-cn' : 1
	}
};