@ModelType YXA20_03Return
@Code
    If Request.IsAjaxRequest Then
        Layout = Nothing
    End If
    Dim ttSemiYear As String = ""
    Dim ttSemistry As String = "1"

    Dim id As String = "_" + Guid.NewGuid.ToString
End Code



<div id="@id" class="container-page100-white" style="align-items: normal;">
    <!--===============================================================================================-->
    <div class="View1 wrap-login100 p-b-30">
        <div id="divfixed1">
            @*功能名稱*@
            <span class="page100-form-label p-b-5 bo1-ColorM" style="color:black;">
                @*bo1-black*@
                @ViewData("FuncTitle")
            </span>

            @*注意事項*@
            @*<div class="row col-lg-12 col-md-12 col-sm-12 col-xs-12"></div>*@

            @*按鈕*@
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <input id="btExit_re" type="button" class="btn btn-default btn-Color3" value="返回" />
                    <input id="btSave_re" type="button" class="btn btn-default btn-Color1" value="儲存" />
                </div>
            </div>

            @*查詢條件*@
            <div class="b2k-control" id="divNfixed1">
                
                <div class="row">
                    <label class="bo1-ColorM">申請單資料</label>
                    @*@If applyList.Count > 1 Then
                        @<div class="d-flex justify-content-end mb-2" id="navButtons">
                            <button class="btn btn-secondary me-2" id="btnPrev">上一筆</button>
                            <button class="btn btn-secondary" id="btnNext">下一筆</button>
                        </div>
                    End If*@
                   
                </div>

                <div class="row">
                    <div class="col-lg-2 col-md-2 col-sm-4 col-xs-6" style="display:none">
                        <label>申請單號 ：</label>
                        <input id="ApplyNo_re" readonly />
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-4 col-xs-6">
                        <label>場地：</label>
                        <label>@Model.ResName</label>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-4 col-xs-6">
                        <label>申請人：</label>
                        <label>@Model.BorrowerName</label>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-4 col-xs-6">
                        <label>申請人電話：</label>
                        <label>@Model.Phone</label>
                    </div>
                    
                </div>
                <div class=row>
                    <div Class="col-lg-2 col-md-2 col-sm-3 col-xs-6">
                        <label>人事管理費：</label>
                        <label id="lb_Management"></label>
                    </div>
                    <div Class="col-lg-2 col-md-2 col-sm-3 col-xs-6">
                        <label>預估金額：</label>
                        <label id="lb_Price"></label>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                        <label>收保證金：</label>
                        <label id="lb_Deposit"></label>
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                        <label>退保證金：</label>
                        <input type="text" id="tx_DepositRe" />
                    </div>
                </div>
                <div Class="row">
                    <label Class="bo1-ColorM">歸還</label>
                    <table class="table table-bordered" id="TblReTime">
                        <thead>
                            <tr>
                                <th>申請使用時間</th>
                                <th>實際開始時間</th>
                                <th>實際結束時間</th>
                                <th>超時狀態</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>

                    <Table Class="table table-bordered table-striped rwd-table table-warning">
                        <tbody>                            
                            <tr>
                                <td style="width: 10%;">
                                    <label>預估金額：</label>
                                    <label id="lb_ExPrice"></label>
                                </td>
                                <td style="width: 10%;">
                                    <label>實際應繳金額：</label>
                                    <input type="text" id="tx_ActualPrice" />
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <label>歸還狀況：</label>
                                    <input type="text" id="tx_Descript" style="width:90%" />
                                </td>
                            </tr>
                        </tbody>
                    </Table>
                    <table class="table table-bordered table-striped rwd-table tab-primary" style="width:40%">
                        <tbody></tbody>
                    </table>

                </div>

               

            </div>
        </div>
   

        @*資料清單表*@
        <div Class="row b2k-control">
            
        </div>
    </div>
    <div Class="View2 wrap-login100 p-b-30" style="display:none;">

    </div>
</div>

<script type="text/javascript">
    (function ($) {
        $(function () {
            var sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
            var ss_Header_height = $('.limiter').height(); //__Header.vbhtml的高度
            var ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
            var ssfixed1_height = $('#@id #divfixed1').height(); //除了資料清單表資料(功能名稱、注意事項、....)的高度
            var ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表資料的高度
            var ssbFilter_height = 31; //搜尋高度:固定高度無須調整
            var ssInfoEmpty_height = 20; //顯示筆數高度(顯示第 0 至 0 項結果，共 0 項):固定高度無須調整
            var ssTblheader_height = 90; //資料清單表頭高度:不固定請自行調整(當表頭越高，值越大，相反的，當表頭越低，值越小)
            var ssTbl; //資料清單表

            //-----畫面執行-----
            Qry();
            //------------------

            //-----觸發事件-----
            //------------------

            //-----click事件-----
            //返回
            $('#@id #btExit_re').click(function () {
                $(this).trigger('YXA20_03Return_Close');
            })

            //儲存
            $('#@id #btSave_re').click(function () {
                SaveClick();
            });
            //-------------------

            //-----欄位格式設定-----
            //----------------------

            //------dialog------
            //------------------

            //-----function-----
            function Qry() {
                var ttobj = {};
                ttobj.objid = '@Model.objid';

                var urlJSON = '@Url.Action("YXA20_03Retrun_Qry", "YXA20")';
                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppqmodel: ttobj
                    },
                    success: function (data) {
                        $('#@id #TblReTime').find('tbody').detach();
                        $('#@id #TblReTime').append('<tbody>');

                        if (data.OK) {
                            $('#@id #lb_Management').text(data.obj.Management);
                            $('#@id #lb_Price').text(data.obj.Price);
                            $('#@id #lb_Deposit').text(data.obj.Deposit);
                            $('#@id #lb_ExPrice').text(data.obj.Price);

                            $('#@id #tx_DepositRe').val(data.obj.DepositRe);
                            $('#@id #tx_ActualPrice').val(data.obj.ActualPrice);
                            $('#@id #tx_Descript').val(data.obj.Descript);

                            $.map(data.obj.ReturnTimeL, function (item) {
                                var ttAppend = '';
                                ttAppend = F_SettingTr(item)
                                $('#@id #TblReTime').append($(ttAppend));

                                F_Datepicker();
                            });
                            //CreateTbl($('#@id #TblReTime'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
                        } else {
                            alert(data.MSG);
                        }
                    }
                });
            };

            function SaveClick() {
                var ttobj = {};
                ttobj.objid = '@Model.objid';
                ttobj.DepositRe = $('#@id #tx_DepositRe').val();
                ttobj.ActualPrice = $('#@id #tx_ActualPrice').val();
                ttobj.Descript = $('#@id #tx_Descript').val();

                ttobj.ReturnTimeL = [];
                $('#@id #TblReTime').find('.DataRow').each(function () {
                    var ttDtl = {};
                    ttDtl.ReturnDate = $(this).find('.ColReturnD').val();
                    ttDtl.ReturnTime = $(this).find('.ColReturnT').val();
                    ttobj.ReturnTimeL.push(ttDtl);
                });

                var urlJSON = '@Url.Action("YXA20_03Return_Save", "YXA20")';
                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppobj: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            alert("儲存成功");
                            Qry();
                        }
                        else {
                            alert(data.MSG);
                        };
                    }
                })
            }


            function F_SettingTr(ppobj) {
                var ttAppend = '';
                ttAppend = '<tr class="DataRow">'
                    + '<td style="padding:1em; width:8%" data-th="申請使用時間">'
                    + '<label>' + ppobj.ApplyTimeStr + '</label>'
                    //+ '<input class="ColBegD txDatepicker" type="text" style="width:100px;" value=' + ppobj.StartDate + ' />'
                    //+ '<input class="ColBegT" type="time" style="width:120px;" value=' + ppobj.StartTime + ' />'
                    + '</td>'
                    + '<td style="padding:1em; width:8%" data-th="實際開始時間">'
                    + '<label></label>'
                    + '</td>'
                    + '<td style="padding:1em; width:8%" data-th="實際結束時間">'
                    + '<input class="ColReturnD txDatepicker" type="text" style="width:100px;" value=' + ppobj.ReturnDate + ' />'
                    + '<input class="ColReturnT" type="time" style="width:120px;" value=' + ppobj.ReturnTime + ' />'
                    + '</td>'
                    + '<td style="padding:1em; width:30%" data-th="超時狀態">'
                    + '<label></label>'
                    + '</td>'
                    + '</tr>'

                var ttli = $(ttAppend);

                return $(ttli);
            }


            function F_Datepicker() {
                $('#@id .txDatepicker').datepicker({
                    format: "twy/mm/dd",
                    weekStart: 1,
                    maxViewMode: 1,
                    language: "zh-TW"
                });
            }

            function CreateTbl(ppTbl, ppbFilter, ppscrollY) {
                ssTbl = ppTbl.DataTable({
                    "bFilter": ppbFilter,  // 搜尋
                    "bLengthChange": true,
                    "paging": false,        //分頁
                    "bAutoWidth": false,
                    "fixedHeader": true, //表頭固定
                    "scrollY": ppscrollY, // 超過(設定值)時， 顯示卷軸
                    //"scrollX": auto, // auto
                    //固定首列，需要引入相應的dataTables.fixedColumns.min.js
                    "fixedColumns": {
                        "leftColumns": 1 //最左側1列固定
                    },

                    deferRender: true,
                    destroy: true,
                    scroller: true,
                    responsive: false
                });
            }

            $(window).resize(function () {
                sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
                ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
                ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表資料的高度
                $('#@id .View1').css('height', ssView1_height + 'px');
                $('#@id #divNfixed1').css('height', ssNfixed1_height + 'px');
                CreateTbl($('#@id #TblReTime'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
                ssTbl.columns.adjust();
            });
            //------------------


           
        });
    })(jQuery);


</script>
