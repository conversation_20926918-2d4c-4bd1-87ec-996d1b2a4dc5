<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/ico" href="http://www.datatables.net/favicon.ico">
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=2.0">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/syntax/shCore.css">
	<link rel="stylesheet" type="text/css" href="../../../../examples/resources/demo.css">
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/syntax/shCore.js"></script>
	<script type="text/javascript" language="javascript" src="../../../../examples/resources/demo.js"></script>

	<title>Responsive examples - Child row control</title>
</head>

<body class="dt-example">
	<div class="container">
		<section>
			<h1>Responsive example <span>Child row control</span></h1>

			<div class="info">
				<p>When a column is removed from display by Responsive, the data is still available in the table and can be displayed in a DataTables <em>child row</em> (see
				<a href="//datatables.net/reference/api/row().child()"><code class="api" title="DataTables API method">row().child()<span>DT</span></code></a>). By default
				Responsive will show child row controls in the first column when the table has been collapsed, allowing the end user to show / hide the information from the hidden
				columns.</p>

				<p>Responsive has a number of options for display of the child rows:</p>

				<ul class="markdown">
					<li>If child row display is enabled: <a href="//datatables.net/extensions/responsive/reference/option/responsive.details"><code class="option" title=
					"Responsive initialisation option">responsive.details<span>R</span></code></a></li>
					<li>How the show / hide control is displayed: <a href="//datatables.net/extensions/responsive/reference/option/responsive.details.type"><code class="option"
					title="Responsive initialisation option">responsive.details.type<span>R</span></code></a></li>
					<li>How the child row is rendered: <a href="//datatables.net/extensions/responsive/reference/option/responsive.details.renderer"><code class="option" title=
					"Responsive initialisation option">responsive.details.renderer<span>R</span></code></a></li>
				</ul>

				<p>This section shows examples of these options being used.</p>
			</div>
		</section>
	</div>

	<section>
		<div class="footer">
			<div class="gradient"></div>

			<div class="liner">
				<div class="toc">
					<div class="toc-group">
						<h3><a href="./index.html">Child rows</a></h3>
						<ul class="toc">
							<li><a href="./disable-child-rows.html">Disable child rows</a></li>
							<li><a href="./column-control.html">Column controlled child rows</a></li>
							<li><a href="./right-column.html">Column control - right</a></li>
							<li><a href="./whole-row-control.html">Whole row child row control</a></li>
							<li><a href="./custom-renderer.html">Custom child row renderer</a></li>
						</ul>
					</div>
				</div>

				<div class="epilogue">
					<p>Please refer to the <a href="http://www.datatables.net">DataTables documentation</a> for full information about its API properties and methods.<br>
					Additionally, there are a wide range of <a href="http://www.datatables.net/extras">extras</a> and <a href="http://www.datatables.net/plug-ins">plug-ins</a>
					which extend the capabilities of DataTables.</p>

					<p class="copyright">DataTables designed and created by <a href="http://www.sprymedia.co.uk">SpryMedia Ltd</a> &#169; 2007-2015<br>
					DataTables is licensed under the <a href="http://www.datatables.net/mit">MIT license</a>.</p>
				</div>
			</div>
		</div>
	</section>
</body>
</html>