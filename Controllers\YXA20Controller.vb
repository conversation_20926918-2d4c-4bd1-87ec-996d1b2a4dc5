
Namespace Controllers

    Public Class YXA20Controller
        Inherits MyController

        Private Class myResult(Of T)
            Public Property OK As Boolean
            Public Property MSG As String
            Public Property obj As T
        End Class


#Region "YXA20_開放時間設定"

        Function YXA20Main() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try
                ViewData("FuncTitle") = "開放時間設定"

                ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                Else
                    ViewData("SubClass") = ttCodeS
                End If

                'ttCodeS = New List(Of TMyCode)
                'ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                'If Not ttRetStr.StartsWith("OK") Then
                '    ttErrStr.Add(ttRetStr)
                'Else
                '    ViewData("SubClass_2") = ttCodeS
                'End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function

        <HttpPost>
        Public Function YXA20Main_Qry(ppqmodel As YXA20Q) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YXA20Model)
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Main_Qry", "SvcYAcc", "QryTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New List(Of SvcYAcc.WTBResOpenTime)
                    Dim ttWQobj As New SvcYAcc.WQTBResOpenTime

                    ttWQobj.SemiYear = ppqmodel.SemiYear
                    ttWQobj.Semistry = ppqmodel.Semistry
                    ttWQobj.DutyOrgID = ppqmodel.DutyOrgID
                    ttWQobj.ResID = ppqmodel.ResID
                    ttWQobj.SubClassEx = ppqmodel.SubClassEx
                    ttWQobj.HourType = ppqmodel.HourType
                    ttWQobj.Week = ppqmodel.Week
                    ttWQobj.DayOfWeek = ppqmodel.DayOfWeek

                    If Not String.IsNullOrEmpty(ppqmodel.StartDate) AndAlso ppqmodel.StartDate <> "/" Then
                        If CInt(ppqmodel.StartDate.Replace("/", "")) < 19110000 Then
                            ttWQobj.StartDate = CInt(ppqmodel.StartDate.Replace("/", "")) + 19110000
                        Else
                            ttWQobj.StopDate = ppqmodel.StartDate.Replace("/", "")
                        End If
                    End If
                    If Not String.IsNullOrEmpty(ppqmodel.StartTime) Then
                        ttWQobj.StartTime = ppqmodel.StartTime.Replace(":", "")
                    End If


                    If Not String.IsNullOrEmpty(ppqmodel.EndDate) AndAlso ppqmodel.EndDate <> "/" Then
                        If CInt(ppqmodel.EndDate.Replace("/", "")) < 19110000 Then
                            ttWQobj.StopDate = CInt(ppqmodel.EndDate.Replace("/", "")) + 19110000
                        Else
                            ttWQobj.StopDate = ppqmodel.EndDate.Replace("/", "")
                        End If
                    End If
                    If Not String.IsNullOrEmpty(ppqmodel.EndTime) Then
                        ttWQobj.StopTime = ppqmodel.EndTime.Replace(":", "")
                    End If

                    ttWQobj.BorrowerID = ppqmodel.BorrowerID

                    ttRetStr = client.QryTBResOpenTime(ttWPLD, ttWQobj, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                            For Each ttrobj In ttWObj
                                Dim ttsobj As New YXA20Model
                                ttsobj.objid = ttrobj.objid
                                ttsobj.SemiYear = ttrobj.SemiYear
                                ttsobj.Semistry = ttrobj.Semistry
                                ttsobj.DutyOrgID = ttrobj.DutyOrgID
                                ttsobj.DutyOrgName = ttrobj.DutyOrgName
                                ttsobj.ResID = ttrobj.ResID
                                ttsobj.ResName = ttrobj.ResName
                                ttsobj.SubClassEx = ttrobj.SubClassEx
                                ttsobj.HourType = ttrobj.HourType
                                Select Case ttsobj.HourType
                                    Case "A"
                                        ttsobj.HourTypeStr = "整學期"
                                    Case "B"
                                        ttsobj.HourTypeStr = "單週"
                                    Case "C"
                                        ttsobj.HourTypeStr = "雙週"
                                    Case "D"
                                        ttsobj.HourTypeStr = "指定週"
                                    Case "E"
                                        ttsobj.HourTypeStr = "指定日期"
                                End Select
                                ttsobj.Week = ttrobj.Week
                                ttsobj.DayOfWeek = ttrobj.DayOfWeek

                                Dim ttST As String = ""
                                If Not String.IsNullOrEmpty(ttrobj.StartDate) Then
                                    If ttrobj.StartDate.Length = 8 Then
                                        ttsobj.StartDate = CStr(CInt(Mid(ttrobj.StartDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 5, 2) + "/" + Mid(ttrobj.StartDate, 7, 2)
                                    ElseIf ttrobj.StartDate.Length = 7 Then
                                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 3) + "/" + Mid(ttrobj.StartDate, 4, 2) + "/" + Mid(ttrobj.StartDate, 6, 2)
                                    ElseIf ttrobj.StartDate.Length = 6 Then
                                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 3, 2) + "/" + Mid(ttrobj.StartDate, 5, 2)
                                    ElseIf IsNumeric(ttrobj.StartDate) AndAlso CInt(ttrobj.StartDate) > 0 Then
                                        ttsobj.StartDate = ttrobj.StartDate
                                    End If
                                End If
                                ttsobj.StartTime = ttrobj.StartTime
                                ttST = ttsobj.StartDate + ttsobj.StartTime

                                Dim ttED As String = ""
                                If Not String.IsNullOrEmpty(ttrobj.StopDate) Then
                                    If ttrobj.StopDate.Length = 8 Then
                                        ttsobj.StopDate = CStr(CInt(Mid(ttrobj.StopDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 5, 2) + "/" + Mid(ttrobj.StopDate, 7, 2)
                                    ElseIf ttrobj.StopDate.Length = 7 Then
                                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 3) + "/" + Mid(ttrobj.StopDate, 4, 2) + "/" + Mid(ttrobj.StopDate, 6, 2)
                                    ElseIf ttrobj.StopDate.Length = 6 Then
                                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 3, 2) + "/" + Mid(ttrobj.StopDate, 5, 2)
                                    ElseIf IsNumeric(ttrobj.StopDate) AndAlso CInt(ttrobj.StopDate) > 0 Then
                                        ttsobj.StopDate = ttrobj.StopDate
                                    End If
                                End If
                                ttsobj.StopTime = ttrobj.StopTime
                                ttED = ttsobj.StopDate + ttsobj.StopTime

                                If Not String.IsNullOrEmpty(ttST) OrElse Not String.IsNullOrEmpty(ttED) Then
                                    ttsobj.OPDTStr = ttST + "～" + ttED
                                Else
                                    ttsobj.OPDTStr = ""
                                End If


                                ttsobj.BorrowerID = ttrobj.BorrowerID
                                Select Case ttsobj.BorrowerID
                                    Case "0"
                                        ttsobj.Borrower = "全體人員"
                                    Case "1"
                                        ttsobj.Borrower = "老師"
                                    Case "2"
                                        ttsobj.Borrower = "學生"
                                    Case "3"
                                        ttsobj.Borrower = "教職員"
                                    Case "4"
                                        ttsobj.Borrower = "社團"
                                    Case "5"
                                        ttsobj.Borrower = "校外單位"
                                    Case "6"
                                        ttsobj.Borrower = "計畫"
                                End Select
                                'ttsobj.Borrower = ttrobj.Borrower
                                ttsobj.Hour = ttrobj.Hour

                                ttMyList.Add(ttsobj)
                            Next
                        End If

                    Else
                        ttErrStr.Add(ttRetStr)
                    End If


                    Session("ssGradeOpenL") = ttMyList
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        <HttpPost>
        Public Function YXA20Main_Del(ppqmodel As YXA20Model) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YXA20Model)
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Main_Del", "SvcYAcc", "DelTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New SvcYAcc.WTBResOpenTime

                    ttWObj.objid = ppqmodel.objid

                    ttRetStr = client.DelTBResOpenTime(ttWPLD, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then


                    Else
                        ttErrStr.Add(ttRetStr)
                    End If
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        ''' <summary>
        ''' 新增開放時段
        ''' </summary>
        Function YXA20Detail(pmodel As YXA20Model) As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try


                If ssFuncID = "B2K.YXA20" Then

                    ViewData("FuncTitle") = "新增開放時段"
                    Dim ttMyList As New YXA20Model
                    ttMyList.objid = pmodel.objid

                    ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                    
                    If Not ttRetStr.StartsWith("OK") Then
                        ttErrStr.Add(ttRetStr)
                    Else
                        ViewData("SubClass") = ttCodeS
                    End If

                    'ttCodeS = New List(Of TMyCode)
                    'ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                    'If Not ttRetStr.StartsWith("OK") Then
                    '    ttErrStr.Add(ttRetStr)
                    'Else
                    '    ViewData("SubClass_2") = ttCodeS
                    'End If

                    Return View(ttMyList)

                Else
                    Return Json(String.Join(vbCrLf, "查無頁面!"), JsonRequestBehavior.AllowGet) 'View("../Shared/Error")
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            Return View()
        End Function

        <HttpPost>
        Public Function YXA20Detail_Qry(ppqmodel As YXA20Model) As JsonResult
            Dim ttResult As JsonResult
            Dim ttErrStr As New List(Of String)
            'Dim ttrobj As List(Of YXA20Model) = Session("ssGradeOpenL")
            Dim ttMyList As New YXA20Model
            Dim ttRetStr As String

            Try
                ttRetStr = ""

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Detail_Qry", "SvcYAcc", "QryTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWobj As New List(Of SvcYAcc.WTBResOpenTime)
                    Dim ttWQobj As New SvcYAcc.WQTBResOpenTime

                    ttWQobj.objid = ppqmodel.objid

                    ttRetStr = client.QryTBResOpenTime(ttWPLD, ttWQobj, ttWobj)


                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                        If ttWobj IsNot Nothing AndAlso ttWobj.Count > 0 Then
                            For Each ttrobj In ttWobj
                                Dim ttsobj As New YXA20Model
                                ttsobj.objid = ttrobj.objid
                                ttsobj.SemiYear = ttrobj.SemiYear
                                ttsobj.Semistry = ttrobj.Semistry
                                ttsobj.DutyOrgID = ttrobj.DutyOrgID
                                ttsobj.DutyOrgName = ttrobj.DutyOrgName
                                ttsobj.ResID = ttrobj.ResID
                                ttsobj.ResName = ttrobj.ResName
                                ttsobj.SubClassEx = ttrobj.SubClassEx
                                ttsobj.HourType = ttrobj.HourType
                                Select Case ttsobj.HourType
                                    Case "A"
                                        ttsobj.HourTypeStr = "整學期"
                                    Case "B"
                                        ttsobj.HourTypeStr = "單週"
                                    Case "C"
                                        ttsobj.HourTypeStr = "雙週"
                                    Case "D"
                                        ttsobj.HourTypeStr = "指定週"
                                    Case "E"
                                        ttsobj.HourTypeStr = "指定日期"
                                End Select
                                ttsobj.Week = ttrobj.Week
                                ttsobj.DayOfWeek = ttrobj.DayOfWeek

                                If Not String.IsNullOrEmpty(ttrobj.StartDate) Then
                                    If ttrobj.StartDate.Length = 8 Then
                                        ttsobj.StartDate = CStr(CInt(Mid(ttrobj.StartDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 5, 2) + "/" + Mid(ttrobj.StartDate, 7, 2)
                                    ElseIf ttrobj.StartDate.Length = 7 Then
                                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 3) + "/" + Mid(ttrobj.StartDate, 4, 2) + "/" + Mid(ttrobj.StartDate, 6, 2)
                                    ElseIf ttrobj.StartDate.Length = 6 Then
                                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 3, 2) + "/" + Mid(ttrobj.StartDate, 5, 2)
                                    ElseIf IsNumeric(ttrobj.StartDate) AndAlso CInt(ttrobj.StartDate) > 0 Then
                                        ttsobj.StartDate = ttrobj.StartDate
                                    End If
                                Else
                                    ttsobj.StartDate = ""
                                End If
                                ttsobj.StartTime = ttrobj.StartTime

                                If Not String.IsNullOrEmpty(ttrobj.StopDate) Then
                                    If ttrobj.StopDate.Length = 8 Then
                                        ttsobj.StopDate = CStr(CInt(Mid(ttrobj.StopDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 5, 2) + "/" + Mid(ttrobj.StopDate, 7, 2)
                                    ElseIf ttrobj.StopDate.Length = 7 Then
                                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 3) + "/" + Mid(ttrobj.StopDate, 4, 2) + "/" + Mid(ttrobj.StopDate, 6, 2)
                                    ElseIf ttrobj.StopDate.Length = 6 Then
                                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 3, 2) + "/" + Mid(ttrobj.StopDate, 5, 2)
                                    ElseIf IsNumeric(ttrobj.StopDate) AndAlso CInt(ttrobj.StopDate) > 0 Then
                                        ttsobj.StopDate = ttrobj.StopDate
                                    End If
                                Else
                                    ttsobj.StopDate = ""
                                End If
                                ttsobj.StopTime = ttrobj.StopTime

                                ttsobj.BorrowerID = ttrobj.BorrowerID
                                Select Case ttsobj.BorrowerID
                                    Case "1"
                                        ttsobj.Borrower = "老師"
                                    Case "2"
                                        ttsobj.Borrower = "學生"
                                    Case "3"
                                        ttsobj.Borrower = "教職員"
                                    Case "4"
                                        ttsobj.Borrower = "社團"
                                    Case "5"
                                        ttsobj.Borrower = "校外單位"
                                    Case "6"
                                        ttsobj.Borrower = "計畫"
                                End Select
                                'ttsobj.Borrower = ttrobj.Borrower
                                ttsobj.Hour = ttrobj.Hour

                                ttMyList = ttsobj
                            Next
                        End If
                    Else
                        ttErrStr.Add(ttRetStr)
                    End If

                End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20Model)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function


        ''' <summary>
        ''' 儲存開放時段
        ''' </summary>
        Public Function YXA20Detail_Save(ppobj As YXA20Model) As ActionResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20Model
            Dim ttErrStr As New List(Of String)
            Dim ttStrs As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Detail_Save", "SvcYAcc", "SaveTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWobj As New SvcYAcc.WTBResOpenTime

                    ttWobj.objid = ppobj.objid
                    ttWobj.SemiYear = ppobj.SemiYear
                    ttWobj.Semistry = ppobj.Semistry
                    ttWobj.DutyOrgID = ppobj.DutyOrgID
                    ttWobj.ResID = ppobj.ResID
                    ttWobj.SubClassEx = ppobj.SubClassEx
                    ttWobj.HourType = ppobj.HourType
                    ttWobj.Week = ppobj.Week
                    ttWobj.DayOfWeek = ppobj.DayOfWeek

                    If Not String.IsNullOrEmpty(ppobj.StartDate) AndAlso ppobj.StartDate <> "/" Then
                        If CInt(ppobj.StartDate.Replace("/", "")) < 19110000 Then
                            ttWobj.StartDate = CInt(ppobj.StartDate.Replace("/", "")) + 19110000
                        Else
                            ttWobj.StartDate = ppobj.StartDate.Replace("/", "")
                        End If
                    End If
                    If Not String.IsNullOrEmpty(ppobj.StartTime) Then
                        ttWobj.StartTime = ppobj.StartTime.Replace(":", "")
                    End If


                    If Not String.IsNullOrEmpty(ppobj.StopDate) AndAlso ppobj.StopDate <> "/" Then
                        If CInt(ppobj.StopDate.Replace("/", "")) < 19110000 Then
                            ttWobj.StopDate = CInt(ppobj.StopDate.Replace("/", "")) + 19110000
                        Else
                            ttWobj.StopDate = ppobj.StopDate.Replace("/", "")
                        End If
                    End If
                    If Not String.IsNullOrEmpty(ppobj.StopTime) Then
                        ttWobj.StopTime = ppobj.StopTime.Replace(":", "")
                    End If

                    ttWobj.BorrowerID = ppobj.BorrowerID
                    ttWobj.Hour = ppobj.Hour

                    ttRetStr = client.SaveTBResOpenTime(ttWPLD, ttWobj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                        If ppobj.objid = "-1" Then
                            ttMyList.objid = ttWobj.objid
                        Else
                            ttMyList.objid = ppobj.objid
                        End If
                    Else
                        ttErrStr.Add(ttRetStr)
                    End If



                End If


            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20Model)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

#End Region

#Region "YXA20_02_收費標準"
        Function YXA20_02() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try
                ViewData("FuncTitle") = "收費標準"

                ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                Else
                    ViewData("SubClass") = ttCodeS
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function

        <HttpPost>
        Public Function YXA20_02_Qry(ppqmodel As YXA20_02Q) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YXA20_02Model)
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_02_Qry", "SvcYAcc", "QryTBResChargeRule")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New List(Of SvcYAcc.WTBResChargeRule)
                    Dim ttWQobj As New SvcYAcc.WQTBResChargeRule

                    ttWQobj.DutyOrgID = ppqmodel.DutyOrgID
                    ttWQobj.ResID = ppqmodel.ResID
                    ttWQobj.SubClassEx = ppqmodel.SubClassEx
                    ttWQobj.BorrowerID = ppqmodel.BorrowerID

                    ttRetStr = client.QryTBResChargeRule(ttWPLD, ttWQobj, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                            For Each ttrobj In ttWObj
                                Dim ttsobj As New YXA20_02Model
                                ttsobj.objid = ttrobj.objid
                                ttsobj.DutyOrgID = ttrobj.DutyOrgID
                                ttsobj.DutyOrgName = ttrobj.DutyOrgName
                                ttsobj.ResID = ttrobj.ResID
                                ttsobj.ResName = ttrobj.ResName
                                ttsobj.SubClassEx = ttrobj.SubClassEx

                                ttsobj.BorrowerID = ttrobj.BorrowerID
                                Select Case ttsobj.BorrowerID
                                    Case "0"
                                        ttsobj.Borrower = "全體人員"
                                    Case "1"
                                        ttsobj.Borrower = "老師"
                                    Case "2"
                                        ttsobj.Borrower = "學生"
                                    Case "3"
                                        ttsobj.Borrower = "教職員"
                                    Case "4"
                                        ttsobj.Borrower = "社團"
                                    Case "5"
                                        ttsobj.Borrower = "校外單位"
                                    Case "6"
                                        ttsobj.Borrower = "計畫"
                                End Select

                                ttsobj.ChargeItem = ttrobj.ChargeItem
                                Select Case ttsobj.ChargeItem
                                    Case "1"
                                        ttsobj.ChargeItemStr = "場地使用費"
                                    Case "2"
                                        ttsobj.ChargeItemStr = "設備使用費"
                                    Case "3"
                                        ttsobj.ChargeItemStr = "清潔費"
                                End Select

                                ttsobj.TimeClass = ttrobj.TimeClass
                                Select Case ttsobj.TimeClass
                                    Case "0"
                                        ttsobj.TimeClassStr = "全天"
                                    Case "1"
                                        ttsobj.TimeClassStr = "日"
                                    Case "2"
                                        ttsobj.TimeClassStr = "夜"
                                    Case "3"
                                        ttsobj.TimeClassStr = "假日"
                                End Select

                                ttsobj.ChargeMethod = ttrobj.ChargeMethod
                                Select Case ttsobj.ChargeMethod
                                    Case "1"
                                        ttsobj.ChargeMethodStr = "次"
                                    Case "2"
                                        ttsobj.ChargeMethodStr = "天"
                                    Case "3"
                                        ttsobj.ChargeMethodStr = "半天"
                                    Case "4"
                                        ttsobj.ChargeMethodStr = "小時"
                                    Case "5"
                                        ttsobj.ChargeMethodStr = "半小時"
                                    Case "6"
                                        ttsobj.ChargeMethodStr = "分鐘"
                                End Select

                                ttsobj.Price = ttrobj.Price
                                ttsobj.LateFeeUnit = ttrobj.LateFeeUnit
                                ttsobj.LateFee = ttrobj.LateFee
                                ttsobj.Management = ttrobj.Management
                                ttsobj.Deposit = ttrobj.Deposit
                                ttsobj.Descript = ttrobj.Descript

                                ttMyList.Add(ttsobj)
                            Next
                        End If

                    Else
                        ttErrStr.Add(ttRetStr)
                    End If


                    Session("ssGradeOpenL") = ttMyList
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20_02Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20_02Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        <HttpPost>
        Public Function YXA20_02_Del(ppqmodel As YXA20_02Model) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YXA20_02Model)
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_02_Del", "SvcYBas", "DelTBResChargeRule")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New SvcYAcc.WTBResChargeRule

                    ttWObj.objid = ppqmodel.objid

                    ttRetStr = client.DelTBResChargeRule(ttWPLD, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then


                    Else
                        ttErrStr.Add(ttRetStr)
                    End If
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20_02Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20_02Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        Function YXA20_02Detail(pmodel As YXA20_02Model) As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)
            Dim ttCode As New TMyCode
            Try


                If ssFuncID = "B2K.YXA20" Then

                    ViewData("FuncTitle") = "收費標準詳細"
                    Dim ttMyList As New YXA20_02Model
                    ttMyList.objid = pmodel.objid

                    ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                    If Not ttRetStr.StartsWith("OK") Then
                        ttErrStr.Add(ttRetStr)
                    Else
                        ViewData("SubClass") = ttCodeS
                    End If

                    ttCodeS = New List(Of TMyCode)
                    ttCode = New TMyCode
                    ttCode.Code = ""
                    ttCode.Title = "請選擇"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "1"
                    ttCode.Title = "場地使用費"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "2"
                    ttCode.Title = "設備使用費"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "3"
                    ttCode.Title = "清潔費"
                    ttCodeS.Add(ttCode)
                    ViewData("ChargeItem") = ttCodeS


                    ttCodeS = New List(Of TMyCode)
                    ttCode = New TMyCode
                    ttCode.Code = ""
                    ttCode.Title = "請選擇"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "1"
                    ttCode.Title = "次"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "2"
                    ttCode.Title = "天"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "3"
                    ttCode.Title = "半天"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "4"
                    ttCode.Title = "小時"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "5"
                    ttCode.Title = "半小時"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "6"
                    ttCode.Title = "分鐘"
                    ttCodeS.Add(ttCode)
                    ViewData("ChargeMethod") = ttCodeS


                    ttCodeS = New List(Of TMyCode)
                    ttCode = New TMyCode
                    ttCode.Code = ""
                    ttCode.Title = "請選擇"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "1"
                    ttCode.Title = "投影機"
                    ttCodeS.Add(ttCode)

                    ttCode = New TMyCode
                    ttCode.Code = "2"
                    ttCode.Title = "投影布幕"
                    ttCodeS.Add(ttCode)
                    ViewData("Equipment") = ttCodeS

                    Return View(ttMyList)

                Else
                    Return Json(String.Join(vbCrLf, "查無頁面!"), JsonRequestBehavior.AllowGet) 'View("../Shared/Error")
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            Return View()
        End Function

        <HttpPost>
        Public Function YXA20_02Detail_Qry(ppqmodel As YXA20_02Model) As JsonResult
            Dim ttResult As JsonResult
            Dim ttErrStr As New List(Of String)
            Dim ttMyList As New YXA20_02Model
            Dim ttRetStr As String

            Try
                ttRetStr = ""

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_02Detail_Qry", "SvcYAcc", "QryTBResChargeRule")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWobj As New List(Of SvcYAcc.WTBResChargeRule)
                    Dim ttWQobj As New SvcYAcc.WQTBResChargeRule

                    ttWQobj.objid = ppqmodel.objid

                    ttRetStr = client.QryTBResChargeRule(ttWPLD, ttWQobj, ttWobj)


                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                        If ttWobj IsNot Nothing AndAlso ttWobj.Count > 0 Then
                            For Each ttrobj In ttWobj
                                Dim ttsobj As New YXA20_02Model
                                ttsobj.objid = ttrobj.objid
                                ttsobj.DutyOrgID = ttrobj.DutyOrgID
                                ttsobj.DutyOrgName = ttrobj.DutyOrgName
                                ttsobj.ResID = ttrobj.ResID
                                ttsobj.ResName = ttrobj.ResName
                                ttsobj.SubClassEx = ttrobj.SubClassEx
                                ttsobj.BorrowerID = ttrobj.BorrowerID
                                Select Case ttsobj.BorrowerID
                                    Case "1"
                                        ttsobj.Borrower = "老師"
                                    Case "2"
                                        ttsobj.Borrower = "學生"
                                    Case "3"
                                        ttsobj.Borrower = "教職員"
                                    Case "4"
                                        ttsobj.Borrower = "社團"
                                    Case "5"
                                        ttsobj.Borrower = "校外單位"
                                    Case "6"
                                        ttsobj.Borrower = "計畫"
                                End Select
                                ttsobj.ChargeItem = ttrobj.ChargeItem
                                ttsobj.TimeClass = ttrobj.TimeClass
                                ttsobj.ChargeMethod = ttrobj.ChargeMethod
                                ttsobj.Price = ttrobj.Price
                                ttsobj.LateFeeUnit = ttrobj.LateFeeUnit
                                ttsobj.LateFee = ttrobj.LateFee
                                ttsobj.Management = ttrobj.Management
                                ttsobj.Deposit = ttrobj.Deposit
                                ttsobj.Descript = ttrobj.Descript

                                ttsobj.IncludedItemS = New List(Of YD_IncludedItem)
                                If ttrobj.IncludedItemS IsNot Nothing AndAlso ttrobj.IncludedItemS.Count > 0 Then
                                    For Each ttyobj In ttrobj.IncludedItemS
                                        Dim ttYD As New YD_IncludedItem
                                        ttYD.ObjectID = ttyobj.ObjectID
                                        ttYD.ChargeItem = ttyobj.ChargeItem
                                        ttYD.ChargeMethod = ttyobj.ChargeMethod
                                        ttYD.Price = ttyobj.Price
                                        ttYD.Descript = ttyobj.Descript
                                        ttsobj.IncludedItemS.Add(ttYD)
                                    Next

                                End If

                                ttMyList = ttsobj
                            Next
                        End If
                    Else
                        ttErrStr.Add(ttRetStr)
                    End If

                End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20_02Model)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20_02Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        Public Function YXA20_02Detail_Save(ppobj As YXA20_02Model) As ActionResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20_02Model
            Dim ttErrStr As New List(Of String)
            Dim ttStrs As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Detail_Save", "SvcYAcc", "SaveTBResChargeRule")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWobj As New SvcYAcc.WTBResChargeRule

                    ttWobj.objid = ppobj.objid
                    ttWobj.DutyOrgID = ppobj.DutyOrgID
                    ttWobj.ResID = ppobj.ResID
                    ttWobj.SubClassEx = ppobj.SubClassEx
                    ttWobj.BorrowerID = ppobj.BorrowerID
                    ttWobj.ChargeItem = ppobj.ChargeItem
                    ttWobj.TimeClass = ppobj.TimeClass
                    ttWobj.ChargeMethod = ppobj.ChargeMethod
                    ttWobj.Price = ppobj.Price
                    ttWobj.LateFeeUnit = ppobj.LateFeeUnit
                    ttWobj.LateFee = ppobj.LateFee
                    ttWobj.Management = ppobj.Management
                    ttWobj.Deposit = ppobj.Deposit
                    ttWobj.Descript = ppobj.Descript

                    ttWobj.IncludedItemS = New List(Of SvcYAcc.WYD_IncludedItem)
                    If ppobj.IncludedItemS IsNot Nothing AndAlso ppobj.IncludedItemS.Count > 0 Then
                        For Each ttyobj In ppobj.IncludedItemS
                            Dim ttYD As New SvcYAcc.WYD_IncludedItem
                            ttYD.ObjectID = ttyobj.ObjectID
                            ttYD.ChargeItem = ttyobj.ChargeItem
                            ttYD.ChargeMethod = ttyobj.ChargeMethod
                            ttYD.Price = ttyobj.Price
                            ttYD.Descript = ttyobj.Descript
                            ttWobj.IncludedItemS.Add(ttYD)
                        Next

                    End If


                    ttRetStr = client.SaveTBResChargeRule(ttWPLD, ttWobj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                        If ppobj.objid = "-1" Then
                            ttMyList.objid = ttWobj.objid
                        Else
                            ttMyList.objid = ppobj.objid
                        End If
                    Else
                        ttErrStr.Add(ttRetStr)
                    End If



                End If


            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20_02Model)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20_02Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function
#End Region

#Region "YXA20_03_場地租借審核"
#Region "首頁"
        Function YXA20_03() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try
                ViewData("FuncTitle") = "場地租借審核"
                ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                Else
                    ViewData("SubClass") = ttCodeS
                End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function

        <HttpPost>
        Public Function YXA20_03_Qry(ppqmodel As YXA20_03Q) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YXA20_03Model)
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_03Main_Qry", "SvcYAcc", "QryTBResChargeRule")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New List(Of SvcYAcc.WTBResChargeRule)
                    Dim ttWQobj As New SvcYAcc.WQTBResChargeRule

                    'ttWQobj.DutyOrgID = ppqmodel.Borrower
                    'ttWQobj.DutyOrgID = ppqmodel.IsPass

                    'ttRetStr = client.QryTBResChargeRule(ttWPLD, ttWQobj, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                            For Each ttrobj In ttWObj
                                Dim ttsobj As New YXA20_03Model
                                'ttsobj.objid = ttrobj.objid
                                'ttsobj.DutyOrgID = ttrobj.DutyOrgID
                                'ttsobj.DutyOrgName = ttrobj.DutyOrgName
                                'ttsobj.ResID = ttrobj.ResID
                                'ttsobj.ResName = ttrobj.ResName
                                'ttsobj.SubClassEx = ttrobj.SubClassEx

                                'ttsobj.BorrowerID = ttrobj.BorrowerID
                                'Select Case ttsobj.BorrowerID
                                '    Case "0"
                                '        ttsobj.Borrower = "全體人員"
                                '    Case "1"
                                '        ttsobj.Borrower = "老師"
                                '    Case "2"
                                '        ttsobj.Borrower = "學生"
                                '    Case "3"
                                '        ttsobj.Borrower = "教職員"
                                '    Case "4"
                                '        ttsobj.Borrower = "社團"
                                '    Case "5"
                                '        ttsobj.Borrower = "校外單位"
                                '    Case "6"
                                '        ttsobj.Borrower = "計畫"
                                'End Select

                                'ttsobj.ChargeItem = ttrobj.ChargeItem
                                'Select Case ttsobj.ChargeItem
                                '    Case "1"
                                '        ttsobj.ChargeItemStr = "場地使用費"
                                '    Case "2"
                                '        ttsobj.ChargeItemStr = "設備使用費"
                                '    Case "3"
                                '        ttsobj.ChargeItemStr = "清潔費"
                                'End Select

                                'ttsobj.TimeClass = ttrobj.TimeClass
                                'Select Case ttsobj.TimeClass
                                '    Case "0"
                                '        ttsobj.TimeClassStr = "全天"
                                '    Case "1"
                                '        ttsobj.TimeClassStr = "日"
                                '    Case "2"
                                '        ttsobj.TimeClassStr = "夜"
                                '    Case "3"
                                '        ttsobj.TimeClassStr = "假日"
                                'End Select

                                'ttsobj.ChargeMethod = ttrobj.ChargeMethod
                                'Select Case ttsobj.ChargeMethod
                                '    Case "1"
                                '        ttsobj.ChargeMethodStr = "次"
                                '    Case "2"
                                '        ttsobj.ChargeMethodStr = "天"
                                '    Case "3"
                                '        ttsobj.ChargeMethodStr = "半天"
                                '    Case "4"
                                '        ttsobj.ChargeMethodStr = "小時"
                                '    Case "5"
                                '        ttsobj.ChargeMethodStr = "半小時"
                                '    Case "6"
                                '        ttsobj.ChargeMethodStr = "分鐘"
                                'End Select

                                'ttsobj.Price = ttrobj.Price
                                'ttsobj.LateFeeUnit = ttrobj.LateFeeUnit
                                'ttsobj.LateFee = ttrobj.LateFee
                                'ttsobj.Management = ttrobj.Management
                                'ttsobj.Deposit = ttrobj.Deposit
                                'ttsobj.Descript = ttrobj.Descript

                                ttMyList.Add(ttsobj)
                            Next
                        End If



                        Dim ttObj As New YXA20_03Model
                        ttObj.objid = "1"
                        ttObj.OpDTobjid = "5"
                        ttObj.DutyOrgID = "A506"
                        ttObj.DutyOrgName = "教務處"
                        ttObj.ResID = "0002"
                        ttObj.ResName = "2教室"
                        ttObj.SubClassEx = ""
                        ttObj.BorrowerName = "測試學生"
                        ttObj.OPDTStr = "114/07/22 09:00 ~ 114/07/22 12:00"
                        ttObj.Status = "20"
                        ttObj.StatusText = "審核中"
                        'ttObj.Hour = "YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
                        ttMyList.Add(ttObj)
                    Else
                        ttErrStr.Add(ttRetStr)
                    End If

                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20_03Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20_03Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        <HttpPost>
        Public Function YXA20_03_Del(ppqmodel As YXA20_03Model) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YXA20_03Model)
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_03Main_Del", "SvcYAcc", "DelTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New SvcYAcc.WTBResOpenTime

                    ttWObj.objid = ppqmodel.objid

                    'ttRetStr = client.DelTBResOpenTime(ttWPLD, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then


                    Else
                        ttErrStr.Add(ttRetStr)
                    End If
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20_03Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20_03Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function
#End Region

#Region "場地查詢"
        Function YXA20_03Res() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try
                ViewData("FuncTitle") = "場地查詢"
                ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                Else
                    ViewData("SubClass") = ttCodeS
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function

        <HttpPost>
        Public Function YXA20_03Res_Qry(ppqmodel As YXA20_03ResQ) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YXA20_03ResModel)
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_03Res_Qry", "SvcYAcc", "QryTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New List(Of SvcYAcc.WTBResOpenTime)
                    Dim ttWQobj As New SvcYAcc.WQTBResOpenTime

                    'ttWQobj.SemiYear = ppqmodel.SemiYear
                    'ttWQobj.Semistry = ppqmodel.Semistry
                    ttWQobj.DutyOrgID = ppqmodel.DutyOrgID
                    ttWQobj.ResID = ppqmodel.ResID
                    ttWQobj.SubClassEx = ppqmodel.SubClassEx
                    'ttWQobj.HourType = ppqmodel.HourType
                    'ttWQobj.Week = ppqmodel.Week
                    'ttWQobj.DayOfWeek = ppqmodel.DayOfWeek

                    If Not String.IsNullOrEmpty(ppqmodel.StartDate) AndAlso ppqmodel.StartDate <> "/" Then
                        If CInt(ppqmodel.StartDate.Replace("/", "")) < 19110000 Then
                            ttWQobj.StartDate = CInt(ppqmodel.StartDate.Replace("/", "")) + 19110000
                        Else
                            ttWQobj.StopDate = ppqmodel.StartDate.Replace("/", "")
                        End If
                    End If
                    If Not String.IsNullOrEmpty(ppqmodel.StartTime) Then
                        ttWQobj.StartTime = ppqmodel.StartTime.Replace(":", "")
                    End If


                    If Not String.IsNullOrEmpty(ppqmodel.EndDate) AndAlso ppqmodel.EndDate <> "/" Then
                        If CInt(ppqmodel.EndDate.Replace("/", "")) < 19110000 Then
                            ttWQobj.StopDate = CInt(ppqmodel.EndDate.Replace("/", "")) + 19110000
                        Else
                            ttWQobj.StopDate = ppqmodel.EndDate.Replace("/", "")
                        End If
                    End If
                    If Not String.IsNullOrEmpty(ppqmodel.EndTime) Then
                        ttWQobj.StopTime = ppqmodel.EndTime.Replace(":", "")
                    End If

                    ttWQobj.BorrowerID = ppqmodel.BorrowerID

                    ttRetStr = client.QryTBResOpenTime(ttWPLD, ttWQobj, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                            For Each ttrobj In ttWObj
                                Dim ttsobj As New YXA20_03ResModel
                                ttsobj.objid = ttrobj.objid
                                'ttsobj.SemiYear = ttrobj.SemiYear
                                'ttsobj.Semistry = ttrobj.Semistry
                                ttsobj.DutyOrgID = ttrobj.DutyOrgID
                                ttsobj.DutyOrgName = ttrobj.DutyOrgName
                                ttsobj.ResID = ttrobj.ResID
                                ttsobj.ResName = ttrobj.ResName
                                ttsobj.SubClassEx = ttrobj.SubClassEx
                                ttsobj.HourType = ttrobj.HourType
                                Select Case ttsobj.HourType
                                    Case "A"
                                        ttsobj.HourTypeStr = "整學期"
                                    Case "B"
                                        ttsobj.HourTypeStr = "單週"
                                    Case "C"
                                        ttsobj.HourTypeStr = "雙週"
                                    Case "D"
                                        ttsobj.HourTypeStr = "指定週"
                                    Case "E"
                                        ttsobj.HourTypeStr = "指定日期"
                                End Select
                                ttsobj.Week = ttrobj.Week
                                ttsobj.DayOfWeek = ttrobj.DayOfWeek

                                Dim ttST As String = ""
                                If Not String.IsNullOrEmpty(ttrobj.StartDate) Then
                                    If ttrobj.StartDate.Length = 8 Then
                                        ttsobj.StartDate = CStr(CInt(Mid(ttrobj.StartDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 5, 2) + "/" + Mid(ttrobj.StartDate, 7, 2)
                                    ElseIf ttrobj.StartDate.Length = 7 Then
                                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 3) + "/" + Mid(ttrobj.StartDate, 4, 2) + "/" + Mid(ttrobj.StartDate, 6, 2)
                                    ElseIf ttrobj.StartDate.Length = 6 Then
                                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 3, 2) + "/" + Mid(ttrobj.StartDate, 5, 2)
                                    ElseIf IsNumeric(ttrobj.StartDate) AndAlso CInt(ttrobj.StartDate) > 0 Then
                                        ttsobj.StartDate = ttrobj.StartDate
                                    End If
                                End If
                                ttsobj.StartTime = ttrobj.StartTime
                                ttST = ttsobj.StartDate + ttsobj.StartTime

                                Dim ttED As String = ""
                                If Not String.IsNullOrEmpty(ttrobj.StopDate) Then
                                    If ttrobj.StopDate.Length = 8 Then
                                        ttsobj.StopDate = CStr(CInt(Mid(ttrobj.StopDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 5, 2) + "/" + Mid(ttrobj.StopDate, 7, 2)
                                    ElseIf ttrobj.StopDate.Length = 7 Then
                                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 3) + "/" + Mid(ttrobj.StopDate, 4, 2) + "/" + Mid(ttrobj.StopDate, 6, 2)
                                    ElseIf ttrobj.StopDate.Length = 6 Then
                                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 3, 2) + "/" + Mid(ttrobj.StopDate, 5, 2)
                                    ElseIf IsNumeric(ttrobj.StopDate) AndAlso CInt(ttrobj.StopDate) > 0 Then
                                        ttsobj.StopDate = ttrobj.StopDate
                                    End If
                                End If
                                ttsobj.StopTime = ttrobj.StopTime
                                ttED = ttsobj.StopDate + ttsobj.StopTime

                                If Not String.IsNullOrEmpty(ttST) OrElse Not String.IsNullOrEmpty(ttED) Then
                                    ttsobj.OPDTStr = ttST + "～" + ttED
                                Else
                                    ttsobj.OPDTStr = ""
                                End If


                                ttsobj.BorrowerID = ttrobj.BorrowerID
                                Select Case ttsobj.BorrowerID
                                    Case "0"
                                        ttsobj.Borrower = "全體人員"
                                    Case "1"
                                        ttsobj.Borrower = "老師"
                                    Case "2"
                                        ttsobj.Borrower = "學生"
                                    Case "3"
                                        ttsobj.Borrower = "教職員"
                                    Case "4"
                                        ttsobj.Borrower = "社團"
                                    Case "5"
                                        ttsobj.Borrower = "校外單位"
                                    Case "6"
                                        ttsobj.Borrower = "計畫"
                                End Select
                                'ttsobj.Borrower = ttrobj.Borrower
                                ttsobj.Hour = ttrobj.Hour

                                ttMyList.Add(ttsobj)
                            Next
                        End If

                    Else
                        ttErrStr.Add(ttRetStr)
                    End If


                    Session("ssGradeOpenL") = ttMyList
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20_03ResModel))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20_03ResModel))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function
#End Region

#Region "申請單詳細"
        Function YXA20_03Detail(pmodel As YXA20_03Model) As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try
                If ssFuncID = "B2K.YXA20" Then
                    ViewData("FuncTitle") = "編輯申請單"
                    Dim ttMyList As New YXA20_03Model
                    ttMyList.objid = pmodel.objid
                    ttMyList.OpDTobjid = pmodel.OpDTobjid

                    ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                    If Not ttRetStr.StartsWith("OK") Then
                        ttErrStr.Add(ttRetStr)
                    Else
                        ViewData("SubClass") = ttCodeS
                    End If

                    Return View(ttMyList)
                Else
                    Return Json(String.Join(vbCrLf, "查無頁面!"), JsonRequestBehavior.AllowGet) 'View("../Shared/Error")
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            Return View()
        End Function

        <HttpPost>
        Public Function YXA20_03Detail_Qry(ppqmodel As YXA20_03Model) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20_03Model
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA30Detail_Qry", "SvcYAcc", "QryTBResChargeRule")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New List(Of SvcYAcc.WTBResChargeRule)
                    Dim ttWQobj As New SvcYAcc.WQTBResChargeRule

                    ttWQobj.DutyOrgID = ppqmodel.DutyOrgID
                    ttWQobj.ResID = ppqmodel.ResID
                    ttWQobj.SubClassEx = ppqmodel.SubClassEx
                    ttWQobj.BorrowerID = ppqmodel.BorrowerID

                    'ttRetStr = client.QryTBResChargeRule(ttWPLD, ttWQobj, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                            For Each ttrobj In ttWObj
                                Dim ttsobj As New YXA20_03Model
                                'ttsobj.objid = ttrobj.objid
                                'ttsobj.DutyOrgID = ttrobj.DutyOrgID
                                'ttsobj.DutyOrgName = ttrobj.DutyOrgName
                                'ttsobj.ResID = ttrobj.ResID
                                'ttsobj.ResName = ttrobj.ResName
                                'ttsobj.SubClassEx = ttrobj.SubClassEx

                                'ttsobj.BorrowerID = ttrobj.BorrowerID
                                'Select Case ttsobj.BorrowerID
                                '    Case "0"
                                '        ttsobj.Borrower = "全體人員"
                                '    Case "1"
                                '        ttsobj.Borrower = "老師"
                                '    Case "2"
                                '        ttsobj.Borrower = "學生"
                                '    Case "3"
                                '        ttsobj.Borrower = "教職員"
                                '    Case "4"
                                '        ttsobj.Borrower = "社團"
                                '    Case "5"
                                '        ttsobj.Borrower = "校外單位"
                                '    Case "6"
                                '        ttsobj.Borrower = "計畫"
                                'End Select

                                'ttsobj.ChargeItem = ttrobj.ChargeItem
                                'Select Case ttsobj.ChargeItem
                                '    Case "1"
                                '        ttsobj.ChargeItemStr = "場地使用費"
                                '    Case "2"
                                '        ttsobj.ChargeItemStr = "設備使用費"
                                '    Case "3"
                                '        ttsobj.ChargeItemStr = "清潔費"
                                'End Select

                                'ttsobj.TimeClass = ttrobj.TimeClass
                                'Select Case ttsobj.TimeClass
                                '    Case "0"
                                '        ttsobj.TimeClassStr = "全天"
                                '    Case "1"
                                '        ttsobj.TimeClassStr = "日"
                                '    Case "2"
                                '        ttsobj.TimeClassStr = "夜"
                                '    Case "3"
                                '        ttsobj.TimeClassStr = "假日"
                                'End Select

                                'ttsobj.ChargeMethod = ttrobj.ChargeMethod
                                'Select Case ttsobj.ChargeMethod
                                '    Case "1"
                                '        ttsobj.ChargeMethodStr = "次"
                                '    Case "2"
                                '        ttsobj.ChargeMethodStr = "天"
                                '    Case "3"
                                '        ttsobj.ChargeMethodStr = "半天"
                                '    Case "4"
                                '        ttsobj.ChargeMethodStr = "小時"
                                '    Case "5"
                                '        ttsobj.ChargeMethodStr = "半小時"
                                '    Case "6"
                                '        ttsobj.ChargeMethodStr = "分鐘"
                                'End Select

                                'ttsobj.Price = ttrobj.Price
                                'ttsobj.LateFeeUnit = ttrobj.LateFeeUnit
                                'ttsobj.LateFee = ttrobj.LateFee
                                'ttsobj.Management = ttrobj.Management
                                'ttsobj.Deposit = ttrobj.Deposit
                                'ttsobj.Descript = ttrobj.Descript

                                ttMyList = ttsobj
                            Next
                        End If



                        Dim ttObj As New YXA20_03Model
                        ttObj.objid = "1"
                        ttObj.DutyOrgID = "A506"
                        'ttsobj.DutyOrgName = ttrobj.DutyOrgName
                        ttObj.ResID = "0002"
                        'ttsobj.ResName = ttrobj.ResName
                        ttObj.SubClassEx = ""
                        ttObj.BorrowerID = "2"
                        ttObj.BorrowerName = "測試學生"
                        ttObj.Phone = "0900000000"
                        ttObj.Descript = "備註測試"
                        ttObj.Status = "90"
                        'ttObj.Hour = "YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
                        ttMyList = ttObj

                    Else
                        ttErrStr.Add(ttRetStr)
                    End If


                    Session("ssGradeOpenL") = ttMyList
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20_03Model)
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20_03Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        Public Function YXA20_03Detail_Save(ppobj As YXA20_03Model) As ActionResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20_03Model
            Dim ttErrStr As New List(Of String)
            Dim ttStrs As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Detail_Save", "SvcYAcc", "SaveTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWobj As New SvcYAcc.WTBResOpenTime

                    ttWobj.objid = ppobj.objid
                    ttWobj.DutyOrgID = ppobj.DutyOrgID
                    ttWobj.ResID = ppobj.ResID
                    ttWobj.SubClassEx = ppobj.SubClassEx

                    If Not String.IsNullOrEmpty(ppobj.StartDate) AndAlso ppobj.StartDate <> "/" Then
                        If CInt(ppobj.StartDate.Replace("/", "")) < 19110000 Then
                            ttWobj.StartDate = CInt(ppobj.StartDate.Replace("/", "")) + 19110000
                        Else
                            ttWobj.StartDate = ppobj.StartDate.Replace("/", "")
                        End If
                    End If
                    If Not String.IsNullOrEmpty(ppobj.StartTime) Then
                        ttWobj.StartTime = ppobj.StartTime.Replace(":", "")
                    End If


                    If Not String.IsNullOrEmpty(ppobj.StopDate) AndAlso ppobj.StopDate <> "/" Then
                        If CInt(ppobj.StopDate.Replace("/", "")) < 19110000 Then
                            ttWobj.StopDate = CInt(ppobj.StopDate.Replace("/", "")) + 19110000
                        Else
                            ttWobj.StopDate = ppobj.StopDate.Replace("/", "")
                        End If
                    End If
                    If Not String.IsNullOrEmpty(ppobj.StopTime) Then
                        ttWobj.StopTime = ppobj.StopTime.Replace(":", "")
                    End If

                    ttWobj.BorrowerID = ppobj.BorrowerID
                    ttWobj.Hour = ppobj.Hour

                    'ttRetStr = client.SaveTBResOpenTime(ttWPLD, ttWobj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                        If ppobj.objid = "-1" Then
                            ttMyList.objid = ttWobj.objid
                        Else
                            ttMyList.objid = ppobj.objid
                        End If
                    Else
                        ttErrStr.Add(ttRetStr)
                    End If



                End If


            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20_03Model)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20_03Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        ''' <summary>
        ''' 查詢開放時間
        ''' </summary>
        <HttpPost>
        Public Function YXA20_03Detail_QryOpDT(ppqmodel As YXA20_03Model) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20_03Model
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA30Res_Qry", "SvcYAcc", "QryTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New List(Of SvcYAcc.WTBResOpenTime)
                    Dim ttWQobj As New SvcYAcc.WQTBResOpenTime

                    ttWQobj.objid = ppqmodel.OpDTobjid

                    ttRetStr = client.QryTBResOpenTime(ttWPLD, ttWQobj, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                            For Each ttrobj In ttWObj
                                Dim ttsobj As New YXA20_03Model
                                ttsobj.OpDTobjid = ttrobj.objid
                                ttsobj.OpDTHour = ttrobj.Hour
                                ttsobj.DutyOrgID = ttrobj.DutyOrgID
                                ttsobj.SubClassEx = ttrobj.SubClassEx
                                ttsobj.ResID = ttrobj.ResID
                                ttMyList = ttsobj
                            Next
                        End If

                    Else
                        ttErrStr.Add(ttRetStr)
                    End If

                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20_03Model)
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20_03Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function
#End Region

#Region "歸還"
        Function YXA20_03Return(pmodel As YXA20_03Return) As ActionResult

            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            '--- 撈申請單資料（ApplyNo） ---
            'Dim applyList As List(Of YXA20_03_Model) = Nothing
            Try

                ViewData("FuncTitle") = "場地歸還"
                Dim ttMyList As New YXA20_03Return
                ttMyList.objid = pmodel.objid
                ttMyList.ResName = pmodel.ResName
                ttMyList.BorrowerName = pmodel.BorrowerName
                ttMyList.Phone = pmodel.Phone

                Return View(ttMyList)
                'Dim allApplyData As List(Of YXA20_03_Model) = TryCast(Session("ssApplyData"), List(Of YXA20_03_Model))
                'applyList = allApplyData.Where(Function(x) ppApplyNoList.Contains(x.ApplyNo)).ToList()
                'If applyList IsNot Nothing AndAlso applyList.Any() Then
                '    ViewData("ApplyList") = applyList
                'End If
            Catch ex As Exception

            End Try

            Return View()
        End Function

        <HttpPost>
        Public Function YXA20_03Retrun_Qry(ppqmodel As YXA20_03Return) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20_03Return
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA30Detail_Qry", "SvcYAcc", "QryTBResChargeRule")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient()
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New List(Of SvcYAcc.WTBResChargeRule)
                    Dim ttWQobj As New SvcYAcc.WQTBResChargeRule

                    'ttWQobj.DutyOrgID = ppqmodel.DutyOrgID
                    'ttWQobj.ResID = ppqmodel.ResID
                    'ttWQobj.SubClassEx = ppqmodel.SubClassEx
                    'ttWQobj.BorrowerID = ppqmodel.BorrowerID

                    'ttRetStr = client.QryTBResChargeRule(ttWPLD, ttWQobj, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                            For Each ttrobj In ttWObj
                                Dim ttsobj As New YXA20_03Return
                                'ttsobj.objid = ttrobj.objid
                                'ttsobj.DutyOrgID = ttrobj.DutyOrgID
                                'ttsobj.DutyOrgName = ttrobj.DutyOrgName
                                'ttsobj.ResID = ttrobj.ResID
                                'ttsobj.ResName = ttrobj.ResName
                                'ttsobj.SubClassEx = ttrobj.SubClassEx

                                'ttsobj.BorrowerID = ttrobj.BorrowerID
                                'Select Case ttsobj.BorrowerID
                                '    Case "0"
                                '        ttsobj.Borrower = "全體人員"
                                '    Case "1"
                                '        ttsobj.Borrower = "老師"
                                '    Case "2"
                                '        ttsobj.Borrower = "學生"
                                '    Case "3"
                                '        ttsobj.Borrower = "教職員"
                                '    Case "4"
                                '        ttsobj.Borrower = "社團"
                                '    Case "5"
                                '        ttsobj.Borrower = "校外單位"
                                '    Case "6"
                                '        ttsobj.Borrower = "計畫"
                                'End Select

                                'ttsobj.ChargeItem = ttrobj.ChargeItem
                                'Select Case ttsobj.ChargeItem
                                '    Case "1"
                                '        ttsobj.ChargeItemStr = "場地使用費"
                                '    Case "2"
                                '        ttsobj.ChargeItemStr = "設備使用費"
                                '    Case "3"
                                '        ttsobj.ChargeItemStr = "清潔費"
                                'End Select

                                'ttsobj.TimeClass = ttrobj.TimeClass
                                'Select Case ttsobj.TimeClass
                                '    Case "0"
                                '        ttsobj.TimeClassStr = "全天"
                                '    Case "1"
                                '        ttsobj.TimeClassStr = "日"
                                '    Case "2"
                                '        ttsobj.TimeClassStr = "夜"
                                '    Case "3"
                                '        ttsobj.TimeClassStr = "假日"
                                'End Select

                                'ttsobj.ChargeMethod = ttrobj.ChargeMethod
                                'Select Case ttsobj.ChargeMethod
                                '    Case "1"
                                '        ttsobj.ChargeMethodStr = "次"
                                '    Case "2"
                                '        ttsobj.ChargeMethodStr = "天"
                                '    Case "3"
                                '        ttsobj.ChargeMethodStr = "半天"
                                '    Case "4"
                                '        ttsobj.ChargeMethodStr = "小時"
                                '    Case "5"
                                '        ttsobj.ChargeMethodStr = "半小時"
                                '    Case "6"
                                '        ttsobj.ChargeMethodStr = "分鐘"
                                'End Select

                                'ttsobj.Price = ttrobj.Price
                                'ttsobj.LateFeeUnit = ttrobj.LateFeeUnit
                                'ttsobj.LateFee = ttrobj.LateFee
                                'ttsobj.Management = ttrobj.Management
                                'ttsobj.Deposit = ttrobj.Deposit
                                'ttsobj.Descript = ttrobj.Descript

                                ttMyList = ttsobj
                            Next
                        End If



                        Dim ttobj As New YXA20_03Return
                        ttobj.objid = "1"

                        Dim ttStDate As String = "114/07/25"
                        Dim ttStTime As String = "09:00"
                        Dim ttEdDate As String = "114/07/25"
                        Dim ttEdTime As String = "12:00"
                        Dim ttST As String = ""
                        ttST = ttStDate + ttStTime
                        Dim ttED As String = ""
                        ttED = ttEdDate + ttEdTime

                        ttobj.ReturnTimeL = New List(Of YXA20_03ReTime)
                        Dim ttReDate As String = "114/07/25"
                        Dim ttReTime As String = "13:00"
                        Dim ttReobj As New YXA20_03ReTime
                        ttReobj.StartDate = ttStDate
                        ttReobj.StartTime = ttStTime
                        ttReobj.StopDate = ttEdDate
                        ttReobj.StopTime = ttEdTime
                        ttReobj.ApplyTimeStr = ttST + "～" + ttED

                        ttReobj.ReturnDate = ttReDate
                        ttReobj.ReturnTime = ttReTime
                        ttReobj.ReturnTimeStr = ttReDate + "～" + ttReTime

                        ttobj.ReturnTimeL.Add(ttReobj)

                        ttobj.Management = "500"
                        ttobj.Price = "1250"
                        ttobj.Deposit = "300"
                        ttobj.DepositRe = "200"
                        ttobj.ActualPrice = "1400"
                        ttobj.Descript = "備註測試"

                        ttMyList = ttobj

                    Else
                        ttErrStr.Add(ttRetStr)
                    End If


                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20_03Return)
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20_03Return)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        Public Function YXA20_03Return_Save(ppobj As YXA20_03Return) As ActionResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20_03Return
            Dim ttErrStr As New List(Of String)
            Dim ttStrs As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_03Return_Save", "SvcYAcc", "SaveTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWobj As New SvcYAcc.WTBResOpenTime

                    'ttWobj.objid = ppobj.objid
                    'ttWobj.DutyOrgID = ppobj.DutyOrgID
                    'ttWobj.ResID = ppobj.ResID
                    'ttWobj.SubClassEx = ppobj.SubClassEx

                    'If Not String.IsNullOrEmpty(ppobj.StartDate) AndAlso ppobj.StartDate <> "/" Then
                    '    If CInt(ppobj.StartDate.Replace("/", "")) < 19110000 Then
                    '        ttWobj.StartDate = CInt(ppobj.StartDate.Replace("/", "")) + 19110000
                    '    Else
                    '        ttWobj.StartDate = ppobj.StartDate.Replace("/", "")
                    '    End If
                    'End If
                    'If Not String.IsNullOrEmpty(ppobj.StartTime) Then
                    '    ttWobj.StartTime = ppobj.StartTime.Replace(":", "")
                    'End If


                    'If Not String.IsNullOrEmpty(ppobj.StopDate) AndAlso ppobj.StopDate <> "/" Then
                    '    If CInt(ppobj.StopDate.Replace("/", "")) < 19110000 Then
                    '        ttWobj.StopDate = CInt(ppobj.StopDate.Replace("/", "")) + 19110000
                    '    Else
                    '        ttWobj.StopDate = ppobj.StopDate.Replace("/", "")
                    '    End If
                    'End If
                    'If Not String.IsNullOrEmpty(ppobj.StopTime) Then
                    '    ttWobj.StopTime = ppobj.StopTime.Replace(":", "")
                    'End If

                    'ttWobj.BorrowerID = ppobj.BorrowerID
                    'ttWobj.Hour = ppobj.Hour

                    'ttRetStr = client.SaveTBResOpenTime(ttWPLD, ttWobj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                        If ppobj.objid = "-1" Then
                            ttMyList.objid = ttWobj.objid
                        Else
                            ttMyList.objid = ppobj.objid
                        End If
                    Else
                        ttErrStr.Add(ttRetStr)
                    End If



                End If


            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20_03Return)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20_03Return)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function
#End Region

        ''' <summary>
        ''' 審核(詳細與首頁共用)
        ''' </summary>
        Public Function YXA20_03_ShenCha(ppobj As List(Of YXA20_03Model)) As ActionResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20_03Model
            Dim ttErrStr As New List(Of String)
            Dim ttStrs As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_03_ShenCha", "SvcYAcc", "SaveTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                    '*查資料
                    Dim ttWPLD As New SvcYAcc.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    If ppobj IsNot Nothing AndAlso ppobj.Count > 0 Then
                        For Each ttsobj In ppobj
                            Dim ttWobj As New SvcYAcc.WTBResOpenTime

                            ttWobj.objid = ttsobj.objid
                            'ttWobj.Reason = ttsobj.Reason
                            'ttWobj.Status = ttsobj.Status


                            'ttRetStr = client.SaveTBResOpenTime(ttWPLD, ttWobj)

                            If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                            Else
                                ttErrStr.Add(ttRetStr)
                            End If
                        Next

                    End If





                End If


            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20_03Model)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20_03Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

#End Region

#Region "YXA20_03_場地租借審核(保留)"

        ''' <summary>
        ''' 查詢借用紀錄
        ''' </summary>
        Function YXA20_03Home_() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            ViewData("FuncTitle") = "場地租借審核"

            Return View()
        End Function

        Function YXA20_03Home_Qry_(ppobjid As String, ppDutyOrgID As String) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttErrStr As New List(Of String) '存放錯誤訊息陣列
            Dim ttRetStr As String = "" '存放訊息


            '申請單假資料
            Dim fakeData As New List(Of YXA20_03_Model) From {
            New YXA20_03_Model With {
                .ApplyNo = "APL20250701",
                .DutyOrgName = "教務處",
                .DutyOrgID = "A1",
                .Location = "大禮堂",
                .ApplyTime = "2025/05/16 09:30",
                .Status = "已歸還",
                .objid = "Z3",
                .ApplicantName = "張三",
                .ApplicantPhone = "0912345678",
                .Description = "校內講座活動",
                .Deposit = "1000",
                .SelectedTimes = New List(Of ApplyTimeSlot) From {
                New ApplyTimeSlot With {.ApplyNo = "APL20250701", .DateValue = "2025-06-23", .TimeStart = "09:00", .TimeEnd = "09:30", .Status = "V"},
                New ApplyTimeSlot With {.ApplyNo = "APL20250701", .DateValue = "2025-06-23", .TimeStart = "09:30", .TimeEnd = "10:00", .Status = "V"},
                New ApplyTimeSlot With {.ApplyNo = "APL20250701", .DateValue = "2025-06-23", .TimeStart = "10:00", .TimeEnd = "10:30", .Status = "V"},
                New ApplyTimeSlot With {.ApplyNo = "APL20250701", .DateValue = "2025-06-23", .TimeStart = "10:30", .TimeEnd = "11:00", .Status = "V"}
                }
            },
            New YXA20_03_Model With {
                .ApplyNo = "APL20250702",
                .DutyOrgName = "體育室",
                .Location = "籃球場",
                .ApplyTime = "2025/05/31 14:10",
                .Status = "已核准",
                .objid = "L4",
                .ApplicantName = "李四",
                .ApplicantPhone = "0987654321",
                .Description = "系籃比賽預賽",
                .Deposit = "1000",
                .SelectedTimes = New List(Of ApplyTimeSlot) From {
                New ApplyTimeSlot With {.DateValue = "2025-06-18", .TimeStart = "12:00", .TimeEnd = "12:30", .Status = "V"},
                New ApplyTimeSlot With {.DateValue = "2025-06-18", .TimeStart = "12:30", .TimeEnd = "13:00", .Status = "V"},
                New ApplyTimeSlot With {.DateValue = "2025-06-18", .TimeStart = "13:00", .TimeEnd = "13:30", .Status = "V"},
                New ApplyTimeSlot With {.DateValue = "2025-06-18", .TimeStart = "18:00", .TimeEnd = "18:30", .Status = "V"},
                New ApplyTimeSlot With {.DateValue = "2025-06-19", .TimeStart = "18:00", .TimeEnd = "18:30", .Status = "V"},
                New ApplyTimeSlot With {.DateValue = "2025-06-19", .TimeStart = "18:30", .TimeEnd = "19:00", .Status = "V"},
                New ApplyTimeSlot With {.DateValue = "2025-06-19", .TimeStart = "19:00", .TimeEnd = "19:30", .Status = "V"}
                }
            },
            New YXA20_03_Model With {
                .ApplyNo = "APL20250711",
                .DutyOrgName = "醫學系",
                .Location = "實驗室L201",
                .ApplyTime = "2025/07/10 10:00",
                .Status = "審核中",
                .objid = "Z3",
                .ApplicantName = "張三",
                .ApplicantPhone = "0912345678",
                .Description = "人體生理實驗準備",
                .Deposit = "1000",
                .SelectedTimes = New List(Of ApplyTimeSlot) From {
                New ApplyTimeSlot With {.ApplyNo = "APL20250711", .DateValue = "2025-07-25", .TimeStart = "10:00", .TimeEnd = "10:30", .Status = "V"},
                New ApplyTimeSlot With {.ApplyNo = "APL20250711", .DateValue = "2025-07-25", .TimeStart = "10:30", .TimeEnd = "11:00", .Status = "V"}
                }
            },
            New YXA20_03_Model With {
                .ApplyNo = "APL20250712",
                .DutyOrgName = "醫學系",
                .Location = "醫學演講廳",
                .ApplyTime = "2025/07/11 15:30",
                .Status = "已核准",
                .ApplicantName = "簡子涵",
                .ApplicantPhone = "0911000222",
                .Description = "醫學系成果發表會",
                .Deposit = "3000",
                .SelectedTimes = New List(Of ApplyTimeSlot) From {
                New ApplyTimeSlot With {.ApplyNo = "APL20250712", .DateValue = "2025-07-28", .TimeStart = "14:00", .TimeEnd = "14:30", .Status = "V"},
                New ApplyTimeSlot With {.ApplyNo = "APL20250712", .DateValue = "2025-07-28", .TimeStart = "14:30", .TimeEnd = "15:00", .Status = "V"},
                New ApplyTimeSlot With {.ApplyNo = "APL20250712", .DateValue = "2025-07-28", .TimeStart = "15:00", .TimeEnd = "15:30", .Status = "V"}
                }
            }, New YXA20_03_Model With {
                .ApplyNo = "APL20250709",
                .DutyOrgName = "教務處",
                .Location = "小禮堂",
                .ApplyTime = "2025/07/09 15:30",
                .Status = "不通過",
                .objid = "Z3",
                .ApplicantName = "張三",
                .ApplicantPhone = "0912345678",
                .Description = "音樂成果發表",
                .Deposit = "2000",
                .SelectedTimes = New List(Of ApplyTimeSlot) From {
                New ApplyTimeSlot With {.ApplyNo = "APL20250709", .DateValue = "2025-07-29", .TimeStart = "09:00", .TimeEnd = "09:30", .Status = "V"},
                New ApplyTimeSlot With {.ApplyNo = "APL20250709", .DateValue = "2025-07-29", .TimeStart = "09:30", .TimeEnd = "10:00", .Status = "V"}
            }
            }
        }

            ' 依條件篩選資料
            Dim filteredData = fakeData.Where(Function(x) (String.IsNullOrEmpty(ppobjid) OrElse x.objid = ppobjid) AndAlso
                                                  (String.IsNullOrEmpty(ppDutyOrgID) OrElse x.DutyOrgID = ppDutyOrgID)).ToList()
            Session("ssApplyData") = fakeData

            ' 組成回傳物件
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20_03_Model))
                obj.OK = False
                obj.MSG = "Qry-X-" & String.Join(vbLf, ttErrStr)
                obj.obj = New List(Of YXA20_03_Model)
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20_03_Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = filteredData
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If
            Return ttResult
        End Function

        ''' <summary>
        ''' 查詢開放場地
        ''' </summary>
        Function YXA20_03Main_() As ActionResult

            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            ViewData("FuncTitle") = "查詢開放場地"

            ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
            If Not ttRetStr.StartsWith("OK") Then
                ttErrStr.Add(ttRetStr)
            Else
                ViewData("SubClass") = ttCodeS
            End If

            Return View()
        End Function

        Function YXA20_03Main_Qry_() As JsonResult
            Dim ttResult As New JsonResult
            Dim ttErrStr As New List(Of String) '存放錯誤訊息陣列
            Dim ttRetStr As String = "" '存放訊息


            '場地開放假資料
            Dim fakeData As New List(Of YXA20Model) From {
                New YXA20Model With {
                    .DutyOrgName = "醫學系",
                    .SubClassEx = "實驗室",
                    .ResName = "實驗室L201",
                    .DayOfWeek = "週三、週四、週五",
                    .StartDate = "114/06/01",
                    .StartTime = "10:00",
                    .StopDate = "114/09/21",
                    .StopTime = "22:00",
                    .Borrower = "教職員,學生,校外單位",
                    .Contains = "20"
                },
                New YXA20Model With {
                    .DutyOrgName = "教務處",
                    .SubClassEx = "禮堂",
                    .ResName = "小禮堂",
                    .DayOfWeek = "週二到週五",
                    .StartDate = "114/06/01",
                    .StartTime = "08:00",
                    .StopDate = "114/09/30",
                    .StopTime = "18:00",
                    .Borrower = "教職員,學生",
                    .Contains = "80"
                },
                New YXA20Model With {
                    .DutyOrgName = "體育室",
                    .SubClassEx = "球場",
                    .ResName = "籃球場",
                    .DayOfWeek = "週二到週四",
                    .StartDate = "114/06/01",
                    .StartTime = "08:00",
                    .StopDate = "114/09/30",
                    .StopTime = "21:00",
                    .Borrower = "教職員,學生",
                    .Contains = "100"
                },
                New YXA20Model With {
                    .DutyOrgName = "醫學系",
                    .SubClassEx = "視聽教室",
                    .ResName = "醫學演講廳",
                    .DayOfWeek = "週一到週三",
                    .StartDate = "114/06/01",
                    .StartTime = "10:00",
                    .StopDate = "114/09/21",
                    .StopTime = "22:00",
                    .Borrower = "教職員,學生,校外單位",
                    .Contains = "80"
                }
            }

            Dim filteredData = fakeData
            Session("ssGradeOpenL") = fakeData

            ' 組成回傳物件
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = False
                obj.MSG = "Qry-X-" & String.Join(vbLf, ttErrStr)
                obj.obj = New List(Of YXA20Model)
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = filteredData
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If
            Return ttResult
        End Function

        ''' <summary>
        ''' 詳細頁_時段
        ''' </summary>
        Function YXA20_03Detail_(ppGetReqL As List(Of String)) As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            ViewData("FuncTitle") = "借用申請詳細"

            ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
            If Not ttRetStr.StartsWith("OK") Then
                ttErrStr.Add(ttRetStr)
            Else
                ViewData("SubClass") = ttCodeS
            End If

            '--- 防呆：無傳入條件直接拒絕 ---
            If ppGetReqL Is Nothing OrElse ppGetReqL.Count = 0 Then
                Return Content("未提供任何查詢條件")
            End If

            '--- 模式判斷：1 筆為新增 (ResID)，2 筆以上為審核 (ApplyNo, Loan) ---
            Dim isAddMode As Boolean = False
            Dim applyList As New List(Of YXA20_03_Model)
            Dim gradeList As New List(Of YXA20Model)

            If ppGetReqL.Count = 1 Then
                isAddMode = True
                ViewData("IsAddMode") = True
                Dim ttResName As String = ppGetReqL(0)

                '--- 撈場地設定資料（從 ResID 比對） ---
                Try
                    'Call YXA20_03Main_Qry()
                    Dim allGradeList As List(Of YXA20Model) = TryCast(Session("ssGradeOpenL"), List(Of YXA20Model))
                    gradeList = allGradeList.Where(Function(x) x.ResName = ttResName).ToList()

                    ' 建立預設空白申請單（ApplyNo 尚未產生）
                    If gradeList.Any() Then
                        Dim g = gradeList.First()
                        Dim model As New YXA20_03_Model With {
                    .ApplyNo = "", ' 尚未產生
                    .Location = g.ResName,
                    .DutyOrgName = "",
                    .ApplicantName = "",
                    .ApplicantPhone = "",
                    .EstimatedAmount = "",
                    .Description = "",
                    .Status = "草稿",
                    .SelectedTimes = New List(Of ApplyTimeSlot)
                }
                        applyList.Add(model)
                    End If
                Catch ex As Exception
                End Try

            Else
                '--- 審核模式（從 ApplyNo 撈申請單 & Loan 撈場地） ---
                ViewData("IsAddMode") = False
                Try
                    Dim allApplyData As List(Of YXA20_03_Model) = TryCast(Session("ssApplyData"), List(Of YXA20_03_Model))
                    applyList = allApplyData.Where(Function(x) ppGetReqL.Contains(x.ApplyNo)).ToList()
                Catch ex As Exception
                End Try

                Try
                    'Call YXA20_03Main_Qry()
                    Dim allGradeList As List(Of YXA20Model) = TryCast(Session("ssGradeOpenL"), List(Of YXA20Model))
                    gradeList = allGradeList.Where(Function(x) ppGetReqL.Contains(x.ResName)).ToList()
                Catch ex As Exception
                End Try
            End If

            '--- 傳值到 View ---
            ViewData("ApplyList") = applyList
            ViewData("GradeList") = gradeList

            Return View("YXA20_03Detail")
        End Function


        ''' <summary>
        ''' 歸還
        ''' </summary>
        Function YXA20_03Re(ppApplyNoList As List(Of String)) As ActionResult
            ViewData("FuncTitle") = "1E30_借用申請：歸還"
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            '--- 撈申請單資料（ApplyNo） ---
            Dim applyList As List(Of YXA20_03_Model) = Nothing
            Try
                Dim allApplyData As List(Of YXA20_03_Model) = TryCast(Session("ssApplyData"), List(Of YXA20_03_Model))
                applyList = allApplyData.Where(Function(x) ppApplyNoList.Contains(x.ApplyNo)).ToList()
                If applyList IsNot Nothing AndAlso applyList.Any() Then
                    ViewData("ApplyList") = applyList
                End If
            Catch ex As Exception

            End Try

            Return View()
        End Function

#End Region

#Region "YXA20_04_校外單位管理"
        Function YXA20_04() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try
                ViewData("FuncTitle") = "校外單位管理"

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function


#End Region

        Public Function F_GetCode_SubClass(ByVal ppinfono As String, ByRef ppCodeS As List(Of TMyCode)) As String
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttRetObj As New List(Of Object)
            Dim ttRetVal As String = ""
            Dim ttsvcLog = GetSvcYLogClient()
            Dim SvcYBas As SvcYBas.SvcYBasClient = GetSvcYBasClient()

            Try
                Dim ttYLogPLD As SvcYLog.WGetTokenParam = Nothing
                ''*固定
                '方法2:每次呼叫WCF都要New出來,SetWYLogPLDF 不New
                ttYLogPLD = New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = New SvcYLog.WGetTokenParamF
                SetWYLogPLDF(ttYLogPLD.WGetTokenParamF)
                '*動態
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "F_GetCode_SubClass", "SvcYBas", "GetCodeS2")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
                If ttErrStr IsNot Nothing AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYBasClient()
                    '*查資料
                    Dim ttWPLD As New SvcYBas.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYBas.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New List(Of SvcYBas.WCode)
                    Dim ttWQObj As New SvcYBas.WCode
                    ttWQObj.CodeClass = "SubClassEx"

                    ttRetStr = client.GetCodeS2(ttWPLD, ppinfono, ttWQObj, ttWObj)

                    If Not ttRetStr.StartsWith("OK") Then
                        ttErrStr.Add(ttRetStr)
                    Else
                        '*WCF
                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                            For Each ttrobj In ttWObj
                                Dim ttobj As New TMyCode
                                ttobj.Code = ttrobj.Code
                                ttobj.Title = ttrobj.Title
                                ppCodeS.Add(ttobj)
                            Next
                        End If
                    End If
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            Finally
                ttsvcLog.Close()
                ttsvcLog = Nothing
                SvcYBas.Close()
                SvcYBas = Nothing
            End Try
            If ttErrStr.Count = 0 Then
                ttRetVal = "OK"
            Else
                ttRetVal = String.Join("、", ttErrStr.ToArray)
                ttErrStr.Clear()
            End If
            ttErrStr = Nothing
            Return ttRetVal
        End Function

        <HttpPost>
        Public Function F_GetCode_Res(ppqmodel As YXA20Q) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of TMyCode)
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                Dim ttsvcLog = GetSvcYLogClient()
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "F_GetCode_SubClass", "SvcTest", "GetAllGradeOpen")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    Dim client = GetSvcYBasClient()
                    '*查資料
                    Dim ttWPLD As New SvcYBas.WPLD
                    GetWPLDF(ttWPLD)
                    ttWPLD.WPLDD = New SvcYBas.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                    Dim ttWObj As New List(Of SvcYBas.WResource)
                    Dim ttWQobj As New SvcYBas.WQResource

                    ttWQobj.DutyOrgID = ppqmodel.DutyOrgID
                    ttWQobj.SubClassEx = ppqmodel.SubClassEx

                    ttRetStr = client.GetResource(ttWPLD, "", ttWQobj, ttWObj)

                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then


                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then

                            For Each ttrobj In ttWObj
                                Dim ttobj As New TMyCode
                                ttobj.Code = ttrobj.ResID
                                ttobj.Title = ttrobj.ResName
                                ttMyList.Add(ttobj)
                            Next

                        End If

                    Else
                        ttErrStr.Add(ttRetStr)
                    End If


                    Session("ssGradeOpenL") = ttMyList
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of TMyCode))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of TMyCode))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function


        '<HttpGet>
        'Function downloadv2(fid As String) As ActionResult
        '    Dim retval As ActionResult = Nothing
        '    If Session(fid) IsNot Nothing Then
        '        If TypeOf Session(fid) Is ExportFile Then
        '            Dim ef As ExportFile = Session(fid)
        '            retval = File(ef.filename, ef.contentType, ef.showname)
        '        End If
        '    End If
        '    Return retval
        'End Function



    End Class

End Namespace