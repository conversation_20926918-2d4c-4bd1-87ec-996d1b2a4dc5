@Code
    If Request.IsAjaxRequest Then
        Layout = Nothing
    End If
    Dim ttSemiYear As String = ""
    Dim ttSemistry As String = "1"

    Dim id As String = "_" + Guid.NewGuid.ToString

    Dim ttSCL As List(Of TMyCode) = ViewData("SubClass")
End Code



<div id="@id" class="container-page100-white" style="align-items: normal;">
    <!--===============================================================================================-->
    <div class="View1 wrap-login100 p-b-30">
        <div id="divfixed1">
            @*功能名稱*@
            <span class="page100-form-label p-b-5 bo1-ColorM" style="color:black;">
                @*bo1-black*@
                @ViewData("FuncTitle")
            </span>

            @*注意事項*@
            @*<div class="row col-lg-12 col-md-12 col-sm-12 col-xs-12"></div>*@

            @*按鈕*@
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <input id="btQry" type="button" class="btn btn-default btn-Color1" value="查詢" />
                    <input id="btAdd" type="button" class="btn btn-default btn-Color" value="新增" />
                    <input id="btPass" type="button" class="btn btn-default btn-success" value="通過" />
                    <input id="btNoPass" type="button" class="btn btn-default btn-danger" value="不通過" />
                    
                </div>
            </div>

            @*查詢條件*@
            <div class="b2k-control">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="position:unset !important">
                        <label>權責單位：</label>
                        <select id="ddl_DutyOrg" class="ResQry">
                            <option value="">請選擇</option>
                            <option value="A506">教務處</option>
                            <option value="T999">體育室</option>
                            <option value="T101">醫學系</option>
                        </select>

                        &nbsp;&nbsp;&nbsp;

                        <label>分類：</label>
                        <select id="ddl_SubClassEx" class="ResQry">
                            <option value="">請選擇</option>
                            @code
                                For Each ttobj In ttSCL
                                    @<option value="@ttobj.Code">@ttobj.Title</option>
                                Next
                            End Code
                        </select>

                        &nbsp;&nbsp;&nbsp;

                        <label>場地：</label>
                        <select id="ddl_Res">
                            <option value="">請選擇</option>
                        </select>

                        &nbsp;&nbsp;&nbsp;

                        <label>申請人：</label>
                        <input id="tx_Borrower" style="width:6em;" type="text" />
                    </div>

                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="position:unset !important">
                        <label>租借時間 ： </label>
                        <input type="text" id="tx_StartDate" class="txDatepicker" />
                        <input type="time" id="dt_StartTime" />
                        <label>～</label>
                        <input type="text" id="tx_StopDate" class="txDatepicker" />
                        <input type="time" id="dt_StopTime" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;

                        <label>狀態：</label>
                        <select id="ddl_Status">
                            <option value="">請選擇</option>
                            <option value="20">審核中</option>
                            <option value="90">通過</option>
                            <option value="-90">不通過</option>
                            <option value="100">已歸還</option>
                        </select>
                    </div>

                    @*<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" style="position:unset !important">
                        <label>權責單位：</label>
                        <input type="text" value="" id="DutyOrgName" />
                    </div>
                    <div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                        <label>申請者：</label>
                        <input type="text" value="" id="ApplicantName" />
                    </div>*@
                    @*<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12">
                        <label>申請單號：</label>
                        <input type="text" value="" id="ApplyNo"/>
                    </div>*@
                </div>
                @*<div class="row">
                    <div class="col-md-6">
                        <label>租借時間 ： </label>
                        <input type="text" id="ap_StartDate" class="txDatepicker" />
                        <input type="time" id="ap_StartTime" />
                        <label>～</label>
                        <input type="text" id="ap_StopDate" class="txDatepicker" />
                        <input type="time" id="ap_StopTime" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </div>
                    <div class="col-md-6" style="display: flex !important; align-items: center;">
                        <label>狀態：</label>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="cbNotYet" checked>
                            <label class="form-check-label" for="cbNotYet">審核中</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="cbPass" checked>
                            <label class="form-check-label" for="cbPass">通過</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="cbNopass" checked>
                            <label class="form-check-label" for="cbNopass">不通過</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="cbMarkReturned">
                            <label class="form-check-label" for="cbMarkReturned">已歸還</label>
                        </div>


                    </div>
                </div>*@
            </div>
        </div>
        @*資料清單表*@
        <div class="row b2k-control">
            <div id="divNfixed1" class="cldiv col-lg-12 col-md-12 col-sm-12 col-xs-12" style="overflow-y:auto;">
                <Table id="Tbl" class="table table-bordered table-hover dataTable no-footer rwd-table" style="width:100%;">
                    <thead>
                        <tr role="row">
                            <th style="padding:1em;"><input type="checkbox" class="chkall" style="zoom:1.5">操作</th>
                            <th style="padding:1em;display:none">objid</th>
                            <th style="padding:1em;display:none">OpDTobjid</th>
                            <th style="padding:1em;">權責單位</th>
                            <th style="padding:1em;">場地名稱</th>
                            <th style="padding:1em;">申請人</th>
                            <th style="padding:1em;">租借時間</th>
                            <th style="padding:1em;">審核狀態</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </Table>
            </div>
        </div>

        @*dialog*@
        <div id="ShowNoPassDialog" class="b2k-control">
            <table style="width:100%;">
                <tr>
                    <td style="width:120px; height:40px; text-align:right;">
                        <label style="font-weight:bold;">說明：</label>
                    </td>
                    <td>
                        <textarea id="tx_Reason" style="width:20em;height:5em;resize:none"></textarea>
                    </td>
                </tr>
            </table>
        </div>

    </div>
    <div class="View2 wrap-login100 p-b-30" style="display:none;">

    </div>
    <div class="View3 wrap-login100 p-b-30" style="display:none;">

    </div>
</div>
<script type="text/javascript">
    (function ($) {
        $(function () {
            var sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
            var ss_Header_height = $('.limiter').height(); //__Header.vbhtml的高度
            var ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
            var ssfixed1_height = $('#@id #divfixed1').height(); //除了資料清單表資料(功能名稱、注意事項、....)的高度
            var ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表資料的高度
            var ssbFilter_height = 31; //搜尋高度:固定高度無須調整
            var ssInfoEmpty_height = 20; //顯示筆數高度(顯示第 0 至 0 項結果，共 0 項):固定高度無須調整
            var ssTblheader_height = 90; //資料清單表頭高度:不固定請自行調整(當表頭越高，值越大，相反的，當表頭越低，值越小)
            var ssTbl; //資料清單表

            //-----畫面執行-----
            CreateTbl($('#@id #Tbl'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
            //------------------

            //-----觸發事件-----
            $('#@id>.View2').on('YXA20_03Res_Close', function (evt, opt) { $(this).hide(); $('#@id>.View1').show(); })

            $('#@id>.View2').on("YXA20_03Detail_Close", function () {
                $('#@id>.View2').empty().hide(); // 關掉 Main（避免殘留）
                $('#@id>.View3').hide(); // 關掉 Detail
                $('#@id>.View1').show(); // 顯示首頁
                QryClick();              // ✅ 自動查詢
            });

            $('#@id>.View3').on('YXA20_03Detail_Close', function (evt, opt) { $(this).hide(); $('#@id>.View1').show(); })

            //查詢場地
            $('#@id .ResQry').on('change', function () {
                QryRes();
            });
            //------------------

            //-----欄位格式設定-----
            $('#@id .txDatepicker').datepicker({
                format: "twy/mm/dd",
                weekStart: 1,
                maxViewMode: 1,
                language: "zh-TW"
            });
            //----------------------

            //-----click事件----
            $('#@id #btQry').click(function () {
                QryClick()
            })

            //新增
            $('#@id #btAdd').click(function () {

                var url = '@Url.Action("YXA20_03Res", "YXA20")';
                $.post(url, function (data) {
                    $('#@id>.View1').hide();
                    $('#@id>.View2').empty().show().html(data);
                })
            })

            //刪除
            $('#@id #Tbl').on('click', 'button[class="clDel"]', function () {
                if (confirm('是否確定刪除此筆申請？')) {
                    var ttobj = {};
                    ttobj.objid = $(this).closest('tr').find('.Colobjid').text();

                    var urlJSON = '@Url.Action("YXA20_03_Del", "YXA20")';
                    $.ajax({
                        type: 'POST',
                        url: urlJSON,
                        data: {
                            ppqmodel: ttobj
                        },
                        success: function (data) {
                            if (data.OK) {
                                alert('刪除完畢');
                                QryClick();
                            } else {
                                alert(data.MSG);
                            };
                        }
                    })
                }
            });

            //詳細
            $('#@id #Tbl').on('click', '.clDetail', function () {
                var ttobj = {};
                var pobj = $(this).closest('tr');
                ttobj.objid = $(pobj).find('.Colobjid').text();
                ttobj.OpDTobjid = $(pobj).find('.ColOpDTobjid').text();
                DetailClick(ttobj);
            });

            //通過
            $('#@id #btPass').click(function () {
                if (confirm("您確定要通過選取的資料嗎？") == true) {
                    ShenCha('90');
                }
            });
            //不通過
            $('#@id #btNoPass').click(function () {
                if (confirm("您確定要不通過選取的資料嗎？") == true) {
                    $("#@id #ShowNoPassDialog").css({ "visibility": "visible", "display": "block" });
                    $('#@id #ShowNoPassDialog').dialog('open');

                    $('#@id .ui-dialog-titlebar-close').find('i').detach();
                    $('#@id .ui-dialog-titlebar-close').append('<i class="glyphicon glyphicon-remove" style="top:auto; bottom:1px; left:auto;"></i>');
                }
            });

            //全選
            $('.chkall').click(function (evt) { chkAll(evt, this); });
            //-------------------

            //------dialog------
            $('#@id #ShowNoPassDialog').dialog({
                resizable: false, //寬高不可變動
                autoOpen: false, //初始不顯示
                title: '原因說明', //視窗標題名稱
                appendTo: '#@id',
                height: "auto", //高度
                width: 600, //寬度
                modal: true,
                buttons: [
                    {
                        text: "確定",
                        click: function () {
                            ShenCha('-90');
                            $('#@id #ShowNoPassDialog').dialog("close");
                        },
                        class: "btn btn-default",
                    },
                    {
                        text: "取消",
                        click: function () {
                            $(this).dialog("close");
                        },
                        class: "btn btn-default",
                    }
                ]
            });
            //------------------

            //-----function-----
            function QryClick() {
                var ttobj = {};
                ttobj.DutyOrgID = $('#@id #ddl_DutyOrg').val();
                ttobj.SubClassEx = $('#@id #ddl_SubClassEx').val();
                ttobj.ResID = $('#@id #ddl_Res').val();
                ttobj.Borrower = $('#@id #tx_Borrower').val();
                ttobj.StartDate = $('#@id  #tx_StartDate').val();
                ttobj.StartTime = $('#@id  #dt_StartTime').val();
                ttobj.StopDate = $('#@id  #tx_StopDate').val();
                ttobj.StopTime = $('#@id  #dt_StopTime').val();
                ttobj.Status = $('#@id #ddl_Status').val();

                var urlJSON = '@Url.Action("YXA20_03_Qry", "YXA20")';
                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppqmodel: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            $('#@id #Tbl').find('tbody').detach();
                            $('#Tbl').append('<tbody>');

                            $.map(data.obj, function (item) {
                                var tr = $('<tr class="DataRow">');
                                var tdStr = $('<td class="tools">')
                                if (item.Status == 20) {
                                    tdStr.append('<input id="chkrow" type="checkbox" class="chkrow" style="zoom:1.5" />')
                                }

                                tdStr.append('<button type="button" title="詳細" class="clDetail"><i class="glyphicon glyphicon-pencil"></i></button>');

                                if (item.Status == 0) {
                                    tdStr.append('<button type="button" title="刪除" class="clDel"><i class="glyphicon glyphicon-trash"></i></button>');
                                }
                                tdStr.appendTo(tr);

                                $('<td headers="objid" Class="Colobjid" style="display:none">' + item.objid + ' </td>').appendTo(tr);
                                $('<td headers="OpDTobjid" Class="ColOpDTobjid" style="display:none">' + item.OpDTobjid + ' </td>').appendTo(tr);
                                $('<td headers="DutyOrg" Class="ColDutyOrg" value="' + item.DutyOrgID + '">' + item.DutyOrgName + ' </td>').appendTo(tr);
                                $('<td headers="Res" Class="ColRes" value="' + item.ResID + '">' + item.ResName + '</td>').appendTo(tr);
                                $('<td headers="Borrower" Class="ColBorrower">' + item.BorrowerName + '</td>').appendTo(tr);
                                $('<td headers="OPDT" Class="ColOPDT">' + item.OPDTStr + '</td>').appendTo(tr);
                                $('<td headers="Status" Class="ColStatus" value="' + item.Status + '">' + item.StatusText + '</td>').appendTo(tr);

                                $('#Tbl').append(tr)
                            })

                            CreateTbl($('#@id #Tbl'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
                        } else {
                            alert(data.MSG);
                        };
                    }
                })
            };

            //審核(通過/不通過)
            function ShenCha(ppStatus) {
                var ttobj = [];
                $('#@id #Tbl').find('.DataRow').each(function () {
                    var ttChk = $(this).find('.chkrow').prop('checked');
                    if (ttChk) {
                        var ttobj1 = {};
                        ttobj1.objid = $(this).find('.Colobjid').text();
                        ttobj1.Status = ppStatus;
                        if (ppStatus == '-90') {
                            ttobj1.Reason = $('#@id #tx_Reason').val();
                        }
                        
                        ttobj.push(ttobj1);
                    }
                });

                var urlJSON = '@Url.Action("YXA20_03_ShenCha", "YXA20")';
                var ttShenFen = $("#hdfShenFen").val()
                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppobj: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            alert("審核完畢");
                            QryClick();
                        } else {
                            alert(data.MSG);
                        }
                    }
                });
            };

            //查詢物件(場地/設備)
            function QryRes() {
                var ttobj = {};
                ttobj.DutyOrgID = $('#@id #ddl_DutyOrg').val();
                ttobj.SubClassEx = $('#@id  #ddl_SubClassEx').val();

                var urlJSON = '@Url.Action("F_GetCode_Res", "YXA20")';
                return $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppqmodel: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            var ttOption = '<option value="">請選擇</option>';
                            $.map(data.obj, function (item) {
                                ttOption += '<option value="' + item.Code + '">' + item.Title + '</option>';
                            });

                            $('#@id #ddl_Res').find('option').detach();
                            $('#@id #ddl_Res').append(ttOption);
                        } else {
                            alert(data.MSG);
                        }
                    }
                });
            }

            //詳細
            function DetailClick(ttobj) {
                var url = '@Url.Action("YXA20_03Detail", "YXA20")';
                $.post(url, { pmodel: ttobj }, function (data) {
                    $('#@id>.View1').hide();
                    $('#@id>.View3').empty().show().html(data);
                })
            }

            //全選
            function chkAll(evt, obj) {
                var ck = $(obj).prop('checked');
                //WinG 2022.07.13 + 套 DataTable 後結構改變，改用外層的 div 控制
                $(obj).closest('.cldiv').find('.chkrow').prop('checked', ck);
                //$(obj).closest('table').find(':checkbox').prop('checked', ck);
                evt.stopPropagation(); //停止後續點擊動作
            };

            function CreateTbl(ppTbl, ppbFilter, ppscrollY) {
                ssTbl = ppTbl.DataTable({
                    "bFilter": ppbFilter,  // 搜尋
                    "bLengthChange": true,
                    "paging": false,        //分頁
                    "bAutoWidth": false,
                    "fixedHeader": true, //表頭固定
                    "scrollY": ppscrollY, // 超過(設定值)時， 顯示卷軸
                    //"scrollX": auto, // auto
                    //固定首列，需要引入相應的dataTables.fixedColumns.min.js
                    "fixedColumns": {
                        "leftColumns": 1 //最左側1列固定
                    },

                    deferRender: true,
                    destroy: true,
                    scroller: true,
                    responsive: false
                });
            }

            $(window).resize(function () {
                sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
                ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
                ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表資料的高度
                $('#@id .View1').css('height', ssView1_height + 'px');
                $('#@id #divNfixed1').css('height', ssNfixed1_height + 'px');
                CreateTbl($('#@id #Tbl'), true, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTblheader_height));
                ssTbl.columns.adjust();
            });
            //------------------

        });
    })(jQuery);


</script>
